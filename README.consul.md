# 图片描述服务 - Consul 服务发现架构

## 概述

本项目已升级为基于 Consul 的微服务架构，支持服务发现、负载均衡和动态路由。

## 架构图

```
客户端 -> Nginx网关:80 -> Consul服务发现 -> 微服务:8000
```

## 快速开始

### 1. 环境准备

```bash
# 复制环境变量模板
cp .env.consul.example .env

# 编辑环境变量（必须设置 OPENAI_API_KEY）
nano .env
```

### 2. 启动 Consul 架构

```bash
# 启动完整的 Consul 微服务架构
docker-compose -f docker-compose.consul.yml up -d

# 查看服务状态
docker-compose -f docker-compose.consul.yml ps
```

### 3. 验证服务

```bash
# 检查网关状态
curl http://localhost/gateway/status

# 检查服务发现
curl http://localhost/api/services

# 检查图片描述服务健康状态
curl http://localhost/api/say-img-description/health

# 测试图片描述功能
curl "http://localhost/api/say-img-description/describe?url=https://example.com/image.jpg"
```

## 服务访问

### 主要端点

- **API 网关**: http://localhost
- **Consul UI**: http://localhost:8500
- **图片描述服务**: http://localhost/api/img-describer/

### API 路由规则

- `/api/{service-name}/` - 服务业务接口
- `/api/{service-name}/health` - 健康检查
- `/api/{service-name}/info` - 服务信息
- `/api/services` - 服务发现列表
- `/gateway/status` - 网关状态

## 开发模式

如果需要本地开发，可以使用传统模式：

```bash
# 启动传统架构（用于开发）
docker-compose up -d

# 直接访问服务
curl http://localhost:8000/describe?url=https://example.com/image.jpg
```

## 服务管理

### 查看服务状态

```bash
# 查看所有容器状态
docker-compose -f docker-compose.consul.yml ps

# 查看服务日志
docker-compose -f docker-compose.consul.yml logs img-describer

# 查看 Consul 日志
docker-compose -f docker-compose.consul.yml logs consul
```

### 扩展服务

```bash
# 扩展图片描述服务到 3 个实例
docker-compose -f docker-compose.consul.yml up -d --scale img-describer=3

# Consul 会自动发现新实例，Nginx 会自动负载均衡
```

### 停止服务

```bash
# 停止所有服务
docker-compose -f docker-compose.consul.yml down

# 停止并删除数据卷
docker-compose -f docker-compose.consul.yml down -v
```

## 监控和调试

### Consul UI

访问 http://localhost:8500 查看：
- 服务注册状态
- 健康检查结果
- 服务实例列表
- KV 存储

### 服务健康检查

```bash
# 检查特定服务健康状态
curl http://localhost/api/img-describer/health

# 查看 Consul 中的健康检查
curl http://localhost:8500/v1/health/service/img-describer
```

### 日志查看

```bash
# 实时查看服务日志
docker-compose -f docker-compose.consul.yml logs -f img-describer

# 查看网关日志
docker-compose -f docker-compose.consul.yml logs -f nginx

# 查看 Consul 日志
docker-compose -f docker-compose.consul.yml logs -f consul
```

## 新微服务开发

### 1. 创建新服务

按照需求文档中的规范创建新微服务：

```bash
# 使用模板创建新服务
./create_microservice.sh user-service
```

### 2. 必须实现的接口

每个新微服务必须实现：

- `GET /health` - 健康检查
- `GET /info` - 服务信息

### 3. 环境变量配置

```env
SERVICE_NAME=your-service-name
SERVICE_PORT=8000
SERVICE_TAGS=api,your-domain
CONSUL_HOST=consul
```

### 4. 添加到 Docker Compose

在 `docker-compose.consul.yml` 中添加新服务配置。

## 故障排除

### 常见问题

1. **服务无法注册到 Consul**
   ```bash
   # 检查 Consul 连接
   docker-compose -f docker-compose.consul.yml logs consul
   
   # 检查服务环境变量
   docker-compose -f docker-compose.consul.yml exec say-img-description env | grep CONSUL
   ```

2. **Nginx 无法路由到服务**
   ```bash
   # 检查 consul-template 日志
   docker-compose -f docker-compose.consul.yml logs consul-template
   
   # 检查生成的 Nginx 配置
   docker-compose -f docker-compose.consul.yml exec nginx cat /etc/nginx/conf.d/services.conf
   ```

3. **服务健康检查失败**
   ```bash
   # 直接测试健康检查端点
   docker-compose -f docker-compose.consul.yml exec img-describer wget -qO- http://localhost:8000/health
   ```

### 重置环境

```bash
# 完全重置环境
docker-compose -f docker-compose.consul.yml down -v
docker system prune -f
docker-compose -f docker-compose.consul.yml up -d
```

## 性能优化

### 负载均衡策略

Nginx 默认使用 `least_conn` 策略，可以在 `consul-template/nginx.conf.tpl` 中修改。

### 缓存配置

服务间调用自动包含重试和超时机制，可以在 `service_client.py` 中调整。

### 监控指标

可以添加 Prometheus 监控：

```yaml
# 在 docker-compose.consul.yml 中添加
prometheus:
  image: prom/prometheus
  ports:
    - "9090:9090"
```

## 安全考虑

1. **生产环境**：
   - 启用 Consul ACL
   - 使用 TLS 加密
   - 配置防火墙规则

2. **API 安全**：
   - 添加认证中间件
   - 实施速率限制
   - 启用 CORS 保护

## 扩展功能

- 服务网格（Consul Connect）
- 配置管理（Consul KV）
- 分布式锁
- 事件系统

## 支持

如有问题，请查看：
1. 服务日志
2. Consul UI
3. 网关状态
4. 健康检查结果
