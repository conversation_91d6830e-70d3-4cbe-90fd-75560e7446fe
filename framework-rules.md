# 微服务框架规则 (Framework Rules)

## 📋 概述

本文档定义了基于 Consul 服务发现的微服务架构框架规则，确保所有微服务能够无缝集成到统一的服务网格中。

## 🏗️ 架构原则

### 核心架构
```
客户端 -> Nginx网关:80 -> Consul服务发现 -> 微服务:8000
```

### 设计原则
- **统一端口**: 所有微服务使用 8000 端口
- **服务发现**: 基于 Consul 的自动服务注册和发现
- **健康检查**: 强制实现健康检查端点
- **负载均衡**: 通过 Nginx 实现自动负载均衡
- **容器化**: 所有服务必须支持 Docker 部署

## 📁 项目结构规范

### 必需的目录结构
```
microservice-name/
├── src/
│   └── service_name/
│       ├── __init__.py
│       ├── api.py              # FastAPI应用 + Consul集成
│       ├── consul_service.py   # Consul服务管理 (复用)
│       ├── service_client.py   # 微服务调用客户端 (复用)
│       ├── config.py           # 配置管理
│       ├── logging.py          # 日志配置
│       └── service.py          # 业务逻辑
├── docker-compose.consul.yml   # Consul集成的Docker编排
├── Dockerfile                  # 容器化配置
├── requirements.txt            # Python依赖
├── .env.example               # 环境变量模板
├── run_consul_api.py          # 启动脚本
└── README.md                  # 项目文档
```

## 🔧 必需实现的接口

### 1. 健康检查端点 (强制)
```python
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": SERVICE_NAME,
        "version": "1.0.0",
        "checks": {
            "database": "ok",
            "cache": "ok",
            "external_api": "ok"
        }
    }
```

### 2. 服务信息端点 (强制)
```python
@app.get("/info")
async def service_info():
    return {
        "service_name": consul_service.service_name,
        "service_id": consul_service.service_id,
        "address": consul_service.service_address,
        "port": consul_service.service_port,
        "tags": consul_service.service_tags,
        "version": "1.0.0"
    }
```

### 3. 业务接口规范
```python
# 所有业务接口都应该有统一的错误处理
@app.get("/your-endpoint")
async def your_business_endpoint(param: str = Query(...)):
    try:
        result = await your_business_logic(param)
        return {"data": result, "status": "success"}
    except Exception as e:
        logger.error(RED + f"✗ 业务处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
```

## 🌐 环境变量规范

### 必需的环境变量
```env
# Consul 配置 (必需)
CONSUL_HOST=consul
CONSUL_PORT=8500

# 服务配置 (必需)
SERVICE_NAME=your-service-name    # 服务名，用于路由
SERVICE_PORT=8000                 # 固定端口
SERVICE_TAGS=api,your-domain      # 至少包含'api'标签
ENVIRONMENT=production

# 服务器配置
HOST=0.0.0.0
LOG_LEVEL=INFO
```

### 可选的环境变量
```env
# 数据库配置
DATABASE_URL=****************************************/mydb
REDIS_URL=redis://redis:6379/0

# 业务配置
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30
CACHE_TTL=3600
```

## 🐳 Docker 配置规范

### Dockerfile 模板
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY src/ ./src/
COPY run_consul_api.py .

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD wget --quiet --tries=1 --spider http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "run_consul_api.py"]
```

### Docker Compose 集成
```yaml
your-service:
  build: .
  container_name: your-service-server
  hostname: your-service
  depends_on:
    consul:
      condition: service_healthy
  environment:
    - CONSUL_HOST=consul
    - SERVICE_NAME=your-service
    - SERVICE_PORT=8000
    - SERVICE_TAGS=api,your-domain
    - ENVIRONMENT=production
  env_file:
    - .env
  networks:
    - microservices
  healthcheck:
    test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8000/health"]
    interval: 15s
    timeout: 10s
    retries: 3
    start_period: 30s
  restart: unless-stopped
  # 不暴露端口，只通过网关访问
```

## 📦 必需的依赖包

### requirements.txt 基础依赖
```txt
fastapi
uvicorn[standard]
aiohttp                 # Consul 客户端
redis[hiredis]          # 如果使用 Redis
asyncpg                 # 如果使用 PostgreSQL
colorama                # 日志颜色
python-dotenv           # 环境变量
pydantic
pydantic-settings
tenacity                # 重试机制
pytest                  # 测试
pytest-asyncio          # 异步测试
```

## 🔄 Consul 集成规范

### 1. 服务注册 (必需)
```python
@app.on_event("startup")
async def on_startup():
    # 初始化业务资源
    await init_resources()
    
    # 注册到 Consul
    success = await consul_service.register()
    if not success:
        logger.error(RED + "✗ Consul 服务注册失败")
        # 可以选择是否退出应用
```

### 2. 服务注销 (必需)
```python
@app.on_event("shutdown")
async def on_shutdown():
    # 从 Consul 注销服务
    await consul_service.deregister()
    
    # 清理资源
    await cleanup_resources()
```

### 3. 复用组件
- **consul_service.py**: 直接复制，修改服务名
- **service_client.py**: 直接复制，添加特定服务调用方法
- **run_consul_api.py**: 直接复制，修改导入路径

## 🌍 路由规范

### API 路由规则
- 服务通过网关访问：`/api/{service-name}/endpoint`
- 健康检查：`/api/{service-name}/health`
- 服务信息：`/api/{service-name}/info`

### 示例路由
```
用户服务: /api/user-service/users
订单服务: /api/order-service/orders
支付服务: /api/payment-service/payments
```

## 🔗 服务间调用规范

### 使用 service_client
```python
from .service_client import service_client

# 调用其他服务
async def call_other_service():
    result = await service_client.call_service(
        service_name="target-service",
        endpoint="/endpoint",
        method="GET",
        params={"key": "value"}
    )
    return result
```

### 便捷方法示例
```python
# 在 service_client.py 中添加特定服务的便捷方法
async def get_user_info(self, user_id: str) -> Optional[Dict]:
    """调用用户服务获取用户信息"""
    return await self.get(
        service_name="user-service",
        endpoint=f"/users/{user_id}"
    )

async def create_order(self, order_data: Dict) -> Optional[Dict]:
    """调用订单服务创建订单"""
    return await self.post(
        service_name="order-service",
        endpoint="/orders",
        data=order_data
    )
```

## 📝 日志规范

### 统一日志格式
```python
from .logging import logger, GREEN, RED, YELLOW

# 成功日志
logger.info(GREEN + f"✓ [{SERVICE_NAME}] 操作成功")

# 错误日志
logger.error(RED + f"✗ [{SERVICE_NAME}] 操作失败: {error}")

# 警告日志
logger.warning(YELLOW + f"⚠ [{SERVICE_NAME}] 警告信息")
```

## 🧪 测试规范

### 必需的测试用例
```python
import pytest
from fastapi.testclient import TestClient
from .api import app

client = TestClient(app)

def test_health_endpoint():
    """测试健康检查端点"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"

def test_info_endpoint():
    """测试服务信息端点"""
    response = client.get("/info")
    assert response.status_code == 200
    assert "service_name" in response.json()

@pytest.mark.asyncio
async def test_consul_registration():
    """测试 Consul 服务注册"""
    from .consul_service import consul_service
    success = await consul_service.register()
    assert success is True
```

## 🚀 部署检查清单

### 部署前检查
- [ ] 健康检查端点返回 200
- [ ] 服务信息端点正常
- [ ] 环境变量配置正确
- [ ] Docker 镜像构建成功
- [ ] 依赖包安装完整

### 部署后验证
```bash
# 1. 检查服务注册
curl http://localhost:8500/v1/agent/services

# 2. 检查健康状态
curl http://localhost/api/{service-name}/health

# 3. 测试业务接口
curl http://localhost/api/{service-name}/your-endpoint
```

## 🔒 安全规范

### 基础安全要求
- 不在日志中输出敏感信息
- 使用环境变量管理密钥
- 实施输入验证和输出编码
- 启用 CORS 保护

### 生产环境安全
```python
# API 密钥验证 (可选)
from fastapi import Header, HTTPException

async def verify_api_key(x_api_key: str = Header(None)):
    if x_api_key != os.getenv("API_KEY"):
        raise HTTPException(status_code=401, detail="Invalid API Key")
```

## 📊 监控规范

### 指标暴露 (可选)
```python
@app.get("/metrics")
async def metrics():
    """Prometheus 指标端点"""
    return {
        "requests_total": request_counter,
        "request_duration_seconds": request_duration,
        "active_connections": active_connections
    }
```

## 🎯 命名规范

### 服务命名
- 服务名：`kebab-case`，如 `user-service`、`order-service`
- 容器名：`{service-name}-server`
- Python 模块：`snake_case`，如 `user_service`

### 标签规范
- 必须包含：`api`（用于网关路由识别）
- 业务标签：`user`、`order`、`payment` 等
- 环境标签：`production`、`staging`、`development`

---

## 📚 快速开始模板

使用以下命令快速创建符合规范的新微服务：

```bash
# 1. 使用脚本创建
./create_microservice.sh your-service-name

# 2. 进入目录并配置
cd your-service-name
cp .env.example .env

# 3. 实现业务逻辑
# 编辑 src/your_service_name/service.py
# 编辑 src/your_service_name/api.py

# 4. 测试和部署
python run_consul_api.py
```

遵循这些规范，您的微服务将能够无缝集成到 Consul 服务网格中！
