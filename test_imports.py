#!/usr/bin/env python3
"""测试所有模块的导入是否正确"""
import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """测试所有模块导入"""
    try:
        # 测试配置模块
        from img_describer.config import settings
        print("✓ config 模块导入成功")
        
        # 测试日志模块
        from img_describer.logging import logger, GREEN, RED
        print("✓ logging 模块导入成功")
        
        # 测试其他模块（不实际初始化，只测试导入）
        import img_describer.cache
        print("✓ cache 模块导入成功")
        
        import img_describer.db
        print("✓ db 模块导入成功")
        
        import img_describer.openai_client
        print("✓ openai_client 模块导入成功")
        
        import img_describer.service
        print("✓ service 模块导入成功")
        
        import img_describer.api
        print("✓ api 模块导入成功")
        
        import img_describer.cli
        print("✓ cli 模块导入成功")
        
        # 测试主包
        import img_describer
        print(f"✓ 主包导入成功，版本: {img_describer.__version__}")
        
        print("\n🎉 所有模块导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
