#!/bin/bash
# 快速创建新微服务的脚本模板
# 使用方法: ./create_microservice.sh service-name

set -e

# 检查参数
if [ $# -eq 0 ]; then
    echo "错误: 请提供服务名称"
    echo "使用方法: $0 <service-name>"
    echo "示例: $0 user-service"
    exit 1
fi

SERVICE_NAME=$1
SERVICE_NAME_UNDERSCORE=$(echo $SERVICE_NAME | tr '-' '_')
SERVICE_NAME_CAMEL=$(echo $SERVICE_NAME | sed 's/-\([a-z]\)/\U\1/g')

echo "创建微服务: $SERVICE_NAME"
echo "Python 模块名: $SERVICE_NAME_UNDERSCORE"

# 创建目录结构
mkdir -p $SERVICE_NAME/src/$SERVICE_NAME_UNDERSCORE
cd $SERVICE_NAME

echo "✓ 创建目录结构"

# 创建 __init__.py
cat > src/$SERVICE_NAME_UNDERSCORE/__init__.py << 'EOF'
"""
微服务模块
"""
__version__ = "1.0.0"
EOF

# 创建 config.py
cat > src/$SERVICE_NAME_UNDERSCORE/config.py << 'EOF'
"""
配置管理
"""
import os
from typing import Optional

class Config:
    """应用配置"""
    
    # 服务配置
    SERVICE_NAME: str = os.getenv("SERVICE_NAME", "example-service")
    SERVICE_PORT: int = int(os.getenv("SERVICE_PORT", "8000"))
    HOST: str = os.getenv("HOST", "0.0.0.0")
    
    # Consul 配置
    CONSUL_HOST: str = os.getenv("CONSUL_HOST", "localhost")
    CONSUL_PORT: int = int(os.getenv("CONSUL_PORT", "8500"))
    
    # 数据库配置
    DATABASE_URL: Optional[str] = os.getenv("DATABASE_URL")
    REDIS_URL: Optional[str] = os.getenv("REDIS_URL")
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    # 环境
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")

config = Config()
EOF

# 创建 logging.py
cat > src/$SERVICE_NAME_UNDERSCORE/logging.py << 'EOF'
"""
日志配置
"""
import logging
import sys
from colorama import init, Fore, Style

# 初始化 colorama
init(autoreset=True)

# 颜色定义
GREEN = Fore.GREEN + Style.BRIGHT
RED = Fore.RED + Style.BRIGHT
YELLOW = Fore.YELLOW + Style.BRIGHT
BLUE = Fore.BLUE + Style.BRIGHT
CYAN = Fore.CYAN + Style.BRIGHT

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)
EOF

# 复制 consul_service.py（从 img-describer 复制并修改）
if [ -f "../src/img_describer/consul_service.py" ]; then
    cp ../src/img_describer/consul_service.py src/$SERVICE_NAME_UNDERSCORE/
    echo "✓ 复制 consul_service.py"
else
    echo "警告: 未找到 consul_service.py，请手动复制"
fi

# 复制 service_client.py
if [ -f "../src/img_describer/service_client.py" ]; then
    cp ../src/img_describer/service_client.py src/$SERVICE_NAME_UNDERSCORE/
    echo "✓ 复制 service_client.py"
else
    echo "警告: 未找到 service_client.py，请手动复制"
fi

# 创建 service.py
cat > src/$SERVICE_NAME_UNDERSCORE/service.py << EOF
"""
${SERVICE_NAME_CAMEL} 业务逻辑
"""
from .logging import logger, GREEN, RED
from .config import config

async def init_resources():
    """初始化资源"""
    try:
        # 在这里初始化数据库连接、缓存等资源
        logger.info(GREEN + f"✓ {config.SERVICE_NAME} 资源初始化完成")
    except Exception as e:
        logger.error(RED + f"✗ 资源初始化失败: {e}")
        raise

async def cleanup_resources():
    """清理资源"""
    try:
        # 在这里清理资源
        logger.info(GREEN + f"✓ {config.SERVICE_NAME} 资源清理完成")
    except Exception as e:
        logger.error(RED + f"✗ 资源清理失败: {e}")

# 在这里添加你的业务逻辑函数
async def example_business_logic(data: str) -> str:
    """示例业务逻辑"""
    logger.info(f"处理数据: {data}")
    return f"处理结果: {data}"
EOF

# 创建 api.py
cat > src/$SERVICE_NAME_UNDERSCORE/api.py << EOF
"""
${SERVICE_NAME_CAMEL} FastAPI 应用
"""
from fastapi import FastAPI, HTTPException, Query
from .service import init_resources, cleanup_resources, example_business_logic
from .logging import logger, RED, GREEN
from .consul_service import consul_service
from .service_client import service_client, cleanup_service_client
from .config import config

app = FastAPI(title="${SERVICE_NAME_CAMEL} 服务")

@app.on_event("startup")
async def on_startup():
    """应用启动时的初始化"""
    try:
        # 初始化业务资源
        await init_resources()
        logger.info(GREEN + "✓ 业务资源初始化完成")
        
        # 注册到 Consul
        success = await consul_service.register()
        if success:
            logger.info(GREEN + "✓ Consul 服务注册成功")
        else:
            logger.error(RED + "✗ Consul 服务注册失败")
            
    except Exception as e:
        logger.error(RED + f"✗ 应用启动失败: {e}")
        raise

@app.on_event("shutdown")
async def on_shutdown():
    """应用关闭时的清理"""
    try:
        # 从 Consul 注销服务
        await consul_service.deregister()
        logger.info(GREEN + "✓ Consul 服务注销完成")
        
        # 清理服务客户端
        await cleanup_service_client()
        logger.info(GREEN + "✓ 服务客户端清理完成")
        
        # 清理业务资源
        await cleanup_resources()
        logger.info(GREEN + "✓ 业务资源清理完成")
        
    except Exception as e:
        logger.error(RED + f"✗ 应用关闭清理失败: {e}")

@app.get("/health")
async def health_check():
    """健康检查端点 - Consul 要求"""
    try:
        health_status = await consul_service.health_check()
        return health_status
    except Exception as e:
        logger.error(RED + f"✗ 健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")

@app.get("/info")
async def service_info():
    """服务信息端点"""
    try:
        return consul_service.get_service_info()
    except Exception as e:
        logger.error(RED + f"✗ 获取服务信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 在这里添加你的业务 API 端点
@app.get("/example")
async def example_endpoint(data: str = Query(..., description="示例数据")):
    """示例业务接口"""
    try:
        result = await example_business_logic(data)
        return {"data": data, "result": result}
    except Exception as e:
        logger.error(RED + f"✗ 业务处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
EOF

# 创建启动脚本
cat > run_consul_api.py << EOF
#!/usr/bin/env python3
"""
启动支持 Consul 服务发现的 ${SERVICE_NAME_CAMEL} 服务
"""
import sys
import os
import signal
import asyncio
from typing import Optional

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 导入日志模块
from ${SERVICE_NAME_UNDERSCORE}.logging import logger, GREEN, RED

class ConsulAPIServer:
    """Consul API 服务器管理类"""
    
    def __init__(self):
        self.server: Optional[object] = None
        self.should_exit = False
        
    async def start_server(self):
        """启动服务器"""
        try:
            import uvicorn
            from ${SERVICE_NAME_UNDERSCORE}.api import app
            
            # 服务器配置
            host = os.getenv("HOST", "0.0.0.0")
            port = int(os.getenv("SERVICE_PORT", "8000"))
            
            logger.info(GREEN + f"✓ 启动 ${SERVICE_NAME_CAMEL} Consul API 服务器: {host}:{port}")
            
            # 创建 uvicorn 配置
            config = uvicorn.Config(
                app=app,
                host=host,
                port=port,
                log_level="info",
                access_log=True,
                reload=False
            )
            
            # 创建服务器实例
            server = uvicorn.Server(config)
            self.server = server
            
            # 设置信号处理
            self._setup_signal_handlers()
            
            # 启动服务器
            await server.serve()
            
        except Exception as e:
            logger.error(RED + f"✗ 服务器启动失败: {e}")
            sys.exit(1)
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，准备关闭服务器...")
            self.should_exit = True
            if self.server:
                self.server.should_exit = True
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)

async def main():
    """主函数"""
    logger.info(GREEN + "=" * 50)
    logger.info(GREEN + "启动 ${SERVICE_NAME_CAMEL} 服务 (Consul 模式)")
    logger.info(GREEN + "=" * 50)
    
    # 检查必要的环境变量
    required_env_vars = ["SERVICE_NAME", "CONSUL_HOST"]
    missing_vars = []
    
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(RED + f"✗ 缺少必要的环境变量: {', '.join(missing_vars)}")
        sys.exit(1)
    
    # 显示配置信息
    logger.info(f"服务名称: {os.getenv('SERVICE_NAME')}")
    logger.info(f"服务端口: {os.getenv('SERVICE_PORT', '8000')}")
    logger.info(f"Consul 地址: {os.getenv('CONSUL_HOST')}:{os.getenv('CONSUL_PORT', '8500')}")
    
    # 创建并启动服务器
    server = ConsulAPIServer()
    
    try:
        await server.start_server()
    except KeyboardInterrupt:
        logger.info("收到键盘中断信号")
    except Exception as e:
        logger.error(RED + f"✗ 服务运行异常: {e}")
        sys.exit(1)
    finally:
        logger.info(GREEN + "✓ 服务已关闭")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(RED + f"✗ 程序异常退出: {e}")
        sys.exit(1)
EOF

# 创建 Dockerfile
cat > Dockerfile << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制并安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY src/ ./src/
COPY run_consul_api.py .

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD wget --quiet --tries=1 --spider http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "run_consul_api.py"]
EOF

# 创建 requirements.txt
cat > requirements.txt << 'EOF'
fastapi
uvicorn[standard]
aiohttp
redis[hiredis]
asyncpg
colorama
python-dotenv
pydantic
pydantic-settings
tenacity
pytest
pytest-asyncio
EOF

# 创建环境变量模板
cat > .env.example << EOF
# ${SERVICE_NAME_CAMEL} 服务配置

# Consul 配置
CONSUL_HOST=consul
CONSUL_PORT=8500

# 服务配置
SERVICE_NAME=$SERVICE_NAME
SERVICE_PORT=8000
SERVICE_TAGS=api,${SERVICE_NAME_UNDERSCORE}
ENVIRONMENT=production

# 服务器配置
HOST=0.0.0.0
LOG_LEVEL=INFO

# 数据库配置（如果需要）
DATABASE_URL=****************************************/mydb
REDIS_URL=redis://redis:6379/0
EOF

# 创建 README.md
cat > README.md << EOF
# ${SERVICE_NAME_CAMEL} 微服务

基于 Consul 服务发现的微服务。

## 快速开始

1. 复制环境变量模板：
   \`\`\`bash
   cp .env.example .env
   \`\`\`

2. 编辑环境变量配置

3. 启动服务：
   \`\`\`bash
   python run_consul_api.py
   \`\`\`

## API 端点

- \`GET /health\` - 健康检查
- \`GET /info\` - 服务信息
- \`GET /example\` - 示例业务接口

## Docker 部署

\`\`\`bash
docker build -t $SERVICE_NAME .
docker run -p 8000:8000 --env-file .env $SERVICE_NAME
\`\`\`
EOF

echo ""
echo "✓ 微服务 $SERVICE_NAME 创建完成！"
echo ""
echo "下一步："
echo "1. cd $SERVICE_NAME"
echo "2. cp .env.example .env"
echo "3. 编辑 .env 文件"
echo "4. 实现业务逻辑在 src/$SERVICE_NAME_UNDERSCORE/service.py"
echo "5. 添加 API 端点在 src/$SERVICE_NAME_UNDERSCORE/api.py"
echo "6. python run_consul_api.py"
echo ""
