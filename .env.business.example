# 业务服务环境变量配置模板
# 复制此文件为 .env 并填入实际值

# ================================
# 基础设施连接配置
# ================================

# Consul 服务发现配置
CONSUL_HOST=consul
CONSUL_PORT=8500

# 如果基础设施部署在其他主机，请修改为实际地址
# CONSUL_HOST=*************
# CONSUL_PORT=8500

# ================================
# 服务配置
# ================================

# 服务基本信息
SERVICE_NAME=say-img-description
SERVICE_PORT=8000
SERVICE_TAGS=api,image,ai
ENVIRONMENT=production

# 服务主机配置
HOST=0.0.0.0
LOG_LEVEL=INFO

# ================================
# 数据库连接配置
# ================================

# PostgreSQL 数据库连接
# 如果使用共享的基础设施数据库
DATABASE_URL=****************************************/mydb

# 如果使用独立的业务数据库，请修改为实际配置
# DATABASE_URL=*******************************************************************/business_db

# 如果基础设施在其他主机
# DATABASE_URL=*********************************************/mydb

# Redis 缓存连接
# 如果使用共享的基础设施 Redis
REDIS_URL=redis://redis:6379/0

# 如果使用独立的业务 Redis
# REDIS_URL=redis://business-redis:6379/0

# 如果基础设施在其他主机
# REDIS_URL=redis://*************:6379/0

# ================================
# 业务服务特定配置
# ================================

# OpenAI API 配置（必填）
# 支持 JSON 格式的多个凭证配置
OPENAI_CREDENTIALS=[{"api_key":"your_api_key_here","base_url":"your_base_url_here","model":"your_model_here"}]

# 或者使用单个配置（兼容模式）
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=
OPENAI_MODEL=gpt-4-vision-preview

# 图片处理配置
MAX_IMAGE_SIZE=10485760  # 10MB
SUPPORTED_FORMATS=jpg,jpeg,png,gif,webp
DEFAULT_LANGUAGE=zh-CN

# 缓存配置
CACHE_TTL=3600  # 1小时
CACHE_PREFIX=img_desc

# 数据库配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# ================================
# 可选：独立数据库配置
# ================================

# 如果业务服务需要独立的数据库实例，取消注释并配置
# DB_USER=business_user
# DB_PASSWORD=business_password
# DB_NAME=business_db

# ================================
# 可选：监控和日志配置
# ================================

# 日志配置
# LOG_FORMAT=json
# LOG_FILE=/app/logs/service.log

# 监控配置
# METRICS_ENABLED=true
# METRICS_PORT=9090

# 健康检查配置
# HEALTH_CHECK_INTERVAL=30
# HEALTH_CHECK_TIMEOUT=10

# ================================
# 可选：安全配置
# ================================

# API 密钥（如果需要）
# API_KEY=your_api_key_here

# JWT 配置（如果需要认证）
# JWT_SECRET=your_jwt_secret_here
# JWT_EXPIRATION=3600

# CORS 配置
# CORS_ORIGINS=*
# CORS_METHODS=GET,POST,PUT,DELETE
# CORS_HEADERS=*

# ================================
# 可选：外部服务配置
# ================================

# 如果需要连接其他外部服务
# EXTERNAL_SERVICE_URL=https://api.example.com
# EXTERNAL_SERVICE_KEY=your_external_service_key

# 消息队列配置（如果需要）
# RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/
# KAFKA_BROKERS=kafka:9092

# ================================
# 开发环境特定配置
# ================================

# 开发模式配置（生产环境请设置为 false）
DEBUG=false
RELOAD=false

# 测试配置
# TEST_DATABASE_URL=**************************************************/test_db
# TEST_REDIS_URL=redis://redis:6379/1
