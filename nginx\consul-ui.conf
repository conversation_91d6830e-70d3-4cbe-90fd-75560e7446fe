# Consul UI 反向代理配置
server {
    listen 80;
    server_name localhost;

    location / {
        proxy_pass http://consul:8500;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 缓存设置
        proxy_cache_bypass $http_upgrade;
        proxy_no_cache $http_upgrade;
    }
}
