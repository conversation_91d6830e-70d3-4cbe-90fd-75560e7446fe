# 业务服务环境变量配置

# ================================
# 基础设施连接配置
# ================================

# Consul 服务发现配置
CONSUL_HOST=consul
CONSUL_PORT=8500

# ================================
# 服务配置
# ================================

# 服务基本信息
SERVICE_NAME=say-img-description
SERVICE_PORT=8000
SERVICE_TAGS=api,image,ai
ENVIRONMENT=production

# 服务主机配置
HOST=0.0.0.0
LOG_LEVEL=INFO

# ================================
# 数据库连接配置
# ================================

# PostgreSQL 数据库连接 - 使用外部数据库
DATABASE_URL=**********************************************/lens

# Redis 缓存连接 - 使用外部Redis
REDIS_URL=redis://:ls3956573@************:6379/5

# 兼容原有配置格式
REDIS_HOST=************
REDIS_PORT=6379
REDIS_PASSWORD=ls3956573
REDIS_DB=5

PG_USER=lens
PG_PASSWORD=Ls.3956573
PG_DB=lens
PG_HOST=************
PG_PORT=5432

# ================================
# 业务服务特定配置
# ================================

# OpenAI API 配置（支持多模型）
OPENAI_CREDENTIALS=[{"api_key":"sk-20wUPZL4VpAAPHTW1m6LKlC4jsrlwxLzko8Zbgskk176BVBc","base_url":"http://************:3000/v1","model":"THUDM/GLM-4.1V-9B-Thinking"},{"api_key":"sk-20wUPZL4VpAAPHTW1m6LKlC4jsrlwxLzko8Zbgskk176BVBc","base_url":"http://************:3000/v1","model":"glm-4v-flash"}]

# 图片处理配置
MAX_IMAGE_SIZE=10485760
SUPPORTED_FORMATS=jpg,jpeg,png,gif,webp
DEFAULT_LANGUAGE=zh-CN

# 缓存配置
CACHE_TTL=3600
CACHE_PREFIX=img_desc

# 数据库配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# ================================
# 开发环境特定配置
# ================================

# 开发模式配置
DEBUG=false
RELOAD=false
