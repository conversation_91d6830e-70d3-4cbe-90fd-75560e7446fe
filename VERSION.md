# 版本更新日志

## v1.1.0 (2025-07-30) - 网关路由修复版

### 🔧 修复内容

#### 关键修复
- ✅ **网关路由问题**: 修复了通过API网关访问业务服务时返回默认页面的问题
- ✅ **Consul-template权限**: 修复了consul-template容器权限拒绝错误 (`user: "0:0"`)
- ✅ **Nginx配置冲突**: 解决了nginx配置文件冲突导致的启动失败
- ✅ **重复日志**: 消除了OpenAI成功响应的重复日志记录
- ✅ **JSON格式错误**: 修复了.env文件中中文逗号导致的JSON解析错误

#### 功能增强
- 🚀 **多模型支持**: 添加了THUDM/GLM-4.1V-9B-Thinking模型支持
- 🚀 **智能负载均衡**: 多模型间的智能负载均衡和故障转移
- 🚀 **一键部署**: 提供Windows (PowerShell) 和 Linux/Mac 的自动化部署脚本
- 🚀 **健康检查增强**: 完善的服务健康监控和状态报告

### 📦 更新的文件

#### 配置文件
- `.env.business` - 更新多模型配置
- `docker-compose.infrastructure.yml` - 修复权限问题
- `docker-compose.business.yml` - 添加端口暴露
- `consul-template/nginx.conf.tpl` - 优化路由配置

#### 部署脚本
- `deploy.ps1` - Windows PowerShell 一键部署脚本
- `quick-deploy.sh` - Linux/Mac 一键部署脚本

#### 文档
- `README.md` - 添加一键部署说明
- `DEPLOYMENT.md` - 详细部署指南
- `VERSION.md` - 版本更新日志

#### 代码优化
- `src/img_describer/service.py` - 移除重复日志记录
- `src/img_describer/openai_client_improved.py` - 改进的重试机制

### 🧪 测试验证

#### 功能测试
- ✅ 直接访问业务服务: `http://localhost:8000/describe`
- ✅ 通过网关访问: `http://localhost/api/say-img-description/describe`
- ✅ 健康检查: `http://localhost/api/say-img-description/health`
- ✅ 服务发现: `http://localhost/api/services`

#### 性能测试
- ✅ 响应时间: ~0.05秒
- ✅ 多模型负载均衡正常
- ✅ 故障转移机制正常

### 🚀 部署方式

#### 一键部署（推荐）
```bash
# Windows
.\deploy.ps1 start

# Linux/Mac
./quick-deploy.sh start
```

#### 手动部署
```bash
# 1. 启动基础设施
docker-compose -f docker-compose.infrastructure.yml up -d

# 2. 等待并修复配置
sleep 15
docker restart consul-template
sleep 8
docker exec nginx-gateway nginx -s reload

# 3. 启动业务服务
docker-compose -f docker-compose.business.yml up -d
```

### 🎯 验证部署成功

```bash
# 测试图片描述功能
curl "http://localhost/api/say-img-description/describe?url=https://basket-11.wbbasket.ru/vol1614/part161483/161483769/images/big/1.webp"
```

**预期结果**: 返回图片的中文描述，响应时间约0.05秒

---

## v1.0.0 (2025-07-29) - 初始版本

### 🎉 初始功能
- 基于OpenAI Vision的图片描述服务
- Redis缓存支持
- PostgreSQL持久化
- Consul服务发现
- Nginx API网关
- Docker容器化部署

### 🏗️ 架构特点
- 微服务架构
- 服务发现和注册
- 动态路由配置
- 负载均衡
- 健康检查

---

## 🔮 未来计划

### v1.2.0 (计划中)
- [ ] 添加更多AI模型支持
- [ ] 实现图片格式转换
- [ ] 添加批量处理接口
- [ ] 性能监控和指标收集

### v1.3.0 (计划中)
- [ ] 支持多语言描述
- [ ] 添加图片分类功能
- [ ] 实现缓存预热机制
- [ ] 添加API限流功能
