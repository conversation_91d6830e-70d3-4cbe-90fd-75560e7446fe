# Consul 服务发现环境变量配置模板
# 复制此文件为 .env 并根据实际情况修改配置

# ===========================================
# Consul 配置
# ===========================================
CONSUL_HOST=consul
CONSUL_PORT=8500

# ===========================================
# 服务配置
# ===========================================
SERVICE_NAME=say-img-description
SERVICE_PORT=8000
SERVICE_TAGS=api,image,ai
ENVIRONMENT=production

# 服务器配置
HOST=0.0.0.0
LOG_LEVEL=INFO

# ===========================================
# 数据库配置
# ===========================================
DATABASE_URL=****************************************/mydb

# PostgreSQL 详细配置（如果需要）
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_USER=user
POSTGRES_PASSWORD=password
POSTGRES_DB=mydb

# ===========================================
# Redis 配置
# ===========================================
REDIS_URL=redis://redis:6379/0

# Redis 详细配置（如果需要）
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
# REDIS_PASSWORD=your_redis_password  # 如果启用了密码

# ===========================================
# OpenAI 配置
# ===========================================
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4-vision-preview

# ===========================================
# 应用配置
# ===========================================
# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# 并发配置
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30

# 重试配置
MAX_RETRIES=3
RETRY_DELAY=1

# ===========================================
# 监控和日志配置
# ===========================================
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO
LOG_FORMAT=json

# 健康检查配置
HEALTH_CHECK_INTERVAL=10
HEALTH_CHECK_TIMEOUT=5

# ===========================================
# 安全配置
# ===========================================
# API 密钥（如果需要）
# API_KEY=your_api_key_here

# CORS 配置
CORS_ORIGINS=*
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=*

# ===========================================
# 开发环境配置
# ===========================================
# 开发模式
DEBUG=false
RELOAD=false

# 测试配置
TEST_MODE=false
TEST_DATABASE_URL=****************************************/test_mydb

# ===========================================
# 生产环境配置
# ===========================================
# 工作进程数（生产环境）
WORKERS=1

# 最大请求数
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100

# 内存限制
MEMORY_LIMIT=512M

# ===========================================
# 网络配置
# ===========================================
# 代理配置（如果需要）
# HTTP_PROXY=http://proxy.example.com:8080
# HTTPS_PROXY=https://proxy.example.com:8080
# NO_PROXY=localhost,127.0.0.1

# DNS 配置
# DNS_SERVERS=*******,*******

# ===========================================
# 备份和恢复配置
# ===========================================
# 备份目录
BACKUP_DIR=/app/backups

# 备份保留天数
BACKUP_RETENTION_DAYS=7

# ===========================================
# 第三方服务配置
# ===========================================
# 如果使用其他 AI 服务
# AZURE_OPENAI_API_KEY=your_azure_key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
# AZURE_OPENAI_API_VERSION=2023-12-01-preview

# 如果使用云存储
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_REGION=us-east-1
# S3_BUCKET=your-bucket-name

# ===========================================
# 特性开关
# ===========================================
# 启用特定功能
ENABLE_METRICS=true
ENABLE_TRACING=false
ENABLE_RATE_LIMITING=true
ENABLE_CACHING=true

# ===========================================
# 注意事项
# ===========================================
# 1. 请勿将包含敏感信息的 .env 文件提交到版本控制系统
# 2. 生产环境中请使用强密码和安全的 API 密钥
# 3. 根据实际部署环境调整网络和存储配置
# 4. 定期更新和轮换敏感凭据
# 5. 监控服务健康状态和性能指标
