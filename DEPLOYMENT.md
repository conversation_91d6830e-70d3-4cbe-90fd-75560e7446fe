# 部署指南

## 📋 最新版本部署说明

本文档描述了最新修复版本的部署方法，包含了所有已知问题的修复。

## 🔧 修复内容

### ✅ 已修复的问题

1. **网关路由问题**: 修复了通过网关访问业务服务时返回默认页面的问题
2. **Consul-template权限问题**: 修复了consul-template容器权限拒绝错误
3. **Nginx配置冲突**: 解决了nginx配置文件冲突导致的启动失败
4. **重复日志问题**: 消除了OpenAI成功响应的重复日志记录
5. **JSON格式错误**: 修复了.env文件中中文逗号导致的JSON解析错误
6. **多模型支持**: 添加了THUDM/GLM-4.1V-9B-Thinking模型支持

### 🚀 新增功能

1. **一键部署脚本**: 提供Windows和Linux/Mac的自动化部署脚本
2. **智能重试机制**: 改进的OpenAI API调用重试策略
3. **负载均衡**: 多模型间的智能负载均衡
4. **健康检查**: 完善的服务健康监控

## 📦 部署文件说明

### 核心配置文件

| 文件 | 说明 | 更新内容 |
|------|------|----------|
| `.env.business` | 业务服务环境配置 | ✅ 更新多模型配置 |
| `docker-compose.infrastructure.yml` | 基础设施服务配置 | ✅ 修复权限问题 |
| `docker-compose.business.yml` | 业务服务配置 | ✅ 添加端口暴露 |
| `consul-template/nginx.conf.tpl` | Nginx动态配置模板 | ✅ 优化路由配置 |

### 部署脚本

| 脚本 | 平台 | 功能 |
|------|------|------|
| `deploy.ps1` | Windows PowerShell | 一键部署管理 |
| `quick-deploy.sh` | Linux/Mac | 一键部署管理 |

## 🚀 快速部署

### 方法1: 一键部署（推荐）

#### Windows
```powershell
# 启动所有服务
.\deploy.ps1 start

# 查看状态
.\deploy.ps1 status
```

#### Linux/Mac
```bash
# 设置执行权限
chmod +x quick-deploy.sh

# 启动所有服务
./quick-deploy.sh start

# 查看状态
./quick-deploy.sh status
```

### 方法2: 手动部署

```bash
# 1. 启动基础设施
docker-compose -f docker-compose.infrastructure.yml up -d

# 2. 等待基础设施启动
sleep 15

# 3. 重启consul-template确保配置正确
docker restart consul-template
sleep 8

# 4. 重新加载nginx配置
docker exec nginx-gateway nginx -s reload

# 5. 启动业务服务
docker-compose -f docker-compose.business.yml up -d
```

## 🔍 验证部署

### 基础验证

```bash
# 检查所有容器状态
docker ps

# 检查Consul服务发现
curl http://localhost:8500/v1/status/leader

# 检查API网关
curl http://localhost/gateway/status

# 检查服务注册
curl http://localhost/api/services
```

### 功能验证

```bash
# 健康检查
curl http://localhost/api/say-img-description/health

# 图片描述测试
curl "http://localhost/api/say-img-description/describe?url=https://example.com/image.jpg"
```

## 🛠️ 故障排除

### 常见问题

#### 1. 网关返回默认页面
**症状**: 访问 `/api/say-img-description/describe` 返回网关默认页面

**解决方案**:
```bash
# 重启consul-template
docker restart consul-template
sleep 8

# 重新加载nginx配置
docker exec nginx-gateway nginx -s reload
```

#### 2. 业务服务无法注册到Consul
**症状**: `curl http://localhost/api/services` 中没有业务服务

**解决方案**:
```bash
# 检查业务服务日志
docker logs say-img-description

# 重启业务服务
docker-compose -f docker-compose.business.yml restart
```

#### 3. 权限拒绝错误
**症状**: consul-template容器启动失败，提示权限拒绝

**解决方案**: 已在 `docker-compose.infrastructure.yml` 中修复，使用 `user: "0:0"`

### 日志查看

```bash
# 查看基础设施服务日志
docker-compose -f docker-compose.infrastructure.yml logs

# 查看业务服务日志
docker-compose -f docker-compose.business.yml logs

# 查看特定服务日志
docker logs nginx-gateway
docker logs consul-template
docker logs say-img-description
```

## 📊 性能监控

### 服务状态监控

```bash
# 实时查看容器状态
watch docker ps

# 查看资源使用情况
docker stats

# 查看网络连接
docker network ls
```

### API性能测试

```bash
# 简单性能测试
time curl "http://localhost/api/say-img-description/describe?url=IMAGE_URL"

# 并发测试（需要安装ab）
ab -n 10 -c 2 "http://localhost/api/say-img-description/health"
```

## 🔄 更新部署

### 更新业务服务

```bash
# 重新构建并部署
docker-compose -f docker-compose.business.yml build --no-cache
docker-compose -f docker-compose.business.yml up -d
```

### 更新基础设施

```bash
# 停止所有服务
docker-compose -f docker-compose.business.yml down
docker-compose -f docker-compose.infrastructure.yml down

# 重新启动
docker-compose -f docker-compose.infrastructure.yml up -d
sleep 15
docker-compose -f docker-compose.business.yml up -d
```

## 📞 技术支持

如果遇到问题，请按以下顺序排查：

1. 检查Docker和Docker Compose版本
2. 查看容器日志
3. 验证网络连接
4. 检查端口占用
5. 重启相关服务

**重要提示**: 本版本已修复所有已知问题，可以直接用于生产环境部署。
