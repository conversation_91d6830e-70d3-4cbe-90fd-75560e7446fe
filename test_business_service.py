#!/usr/bin/env python3
"""
测试业务服务的脚本
"""

import requests
import json
import sys

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            print(f"✅ 健康检查通过: {response.json()}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_info():
    """测试服务信息"""
    print("\n🔍 测试服务信息...")
    try:
        response = requests.get("http://localhost:8000/info", timeout=10)
        if response.status_code == 200:
            info = response.json()
            print(f"✅ 服务信息获取成功:")
            print(f"   - 服务名称: {info.get('service_name')}")
            print(f"   - 服务ID: {info.get('service_id')}")
            print(f"   - 地址: {info.get('address')}:{info.get('port')}")
            print(f"   - 标签: {info.get('tags')}")
            return True
        else:
            print(f"❌ 服务信息获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 服务信息获取异常: {e}")
        return False

def test_consul_registration():
    """测试Consul服务注册"""
    print("\n🔍 测试Consul服务注册...")
    try:
        response = requests.get("http://localhost:8500/v1/catalog/services", timeout=10)
        if response.status_code == 200:
            services = response.json()
            if 'say-img-description' in services:
                print(f"✅ 服务已注册到Consul: {services['say-img-description']}")
                return True
            else:
                print(f"❌ 服务未在Consul中找到")
                return False
        else:
            print(f"❌ 无法获取Consul服务列表: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Consul连接异常: {e}")
        return False

def test_image_description():
    """测试图片描述功能"""
    print("\n🔍 测试图片描述功能...")
    
    # 使用一个简单的测试图片
    test_images = [
        "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200",
        "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=200"
    ]
    
    for i, image_url in enumerate(test_images, 1):
        print(f"\n   测试图片 {i}: {image_url}")
        try:
            response = requests.get(f"http://localhost:8000/describe?url={image_url}", timeout=30)
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 描述成功:")
                print(f"      URL: {result.get('url', 'N/A')}")
                description = result.get('description', 'N/A')
                # 限制描述长度以便显示
                if len(description) > 100:
                    description = description[:100] + "..."
                print(f"      描述: {description}")
                return True
            else:
                print(f"   ❌ 描述失败: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"      错误详情: {error_detail}")
                except:
                    print(f"      错误内容: {response.text}")
        except Exception as e:
            print(f"   ❌ 描述异常: {e}")
    
    return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🔍 测试数据库连接...")
    try:
        # 通过健康检查间接测试数据库连接
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            if health_data.get('status') == 'healthy':
                print("✅ 数据库连接正常（通过健康检查验证）")
                return True
        print("❌ 数据库连接可能有问题")
        return False
    except Exception as e:
        print(f"❌ 数据库连接测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试业务服务...")
    print("=" * 60)
    
    tests = [
        ("健康检查", test_health),
        ("服务信息", test_info),
        ("Consul注册", test_consul_registration),
        ("数据库连接", test_database_connection),
        ("图片描述功能", test_image_description),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！业务服务运行正常！")
        print("\n📍 服务访问地址:")
        print("   - 健康检查: http://localhost:8000/health")
        print("   - 服务信息: http://localhost:8000/info")
        print("   - 图片描述: http://localhost:8000/describe?url=IMAGE_URL")
        print("   - Consul UI: http://localhost:8500")
        
        print("\n🧪 测试命令示例:")
        print('   curl "http://localhost:8000/describe?url=https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300"')
        
        sys.exit(0)
    else:
        print(f"⚠️  {total - passed} 项测试失败，请检查服务状态")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        sys.exit(1)
