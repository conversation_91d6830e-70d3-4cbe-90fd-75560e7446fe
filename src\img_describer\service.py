from .cache import RedisCache
from .db import Database
from .openai_client_improved import describe_by_openai_with_queue
from .logging import logger, GREEN

cache = RedisCache()
db = Database()


async def init_resources():
    await cache.init()
    await db.init()


async def describe_image(photo_url: str) -> str:
    # 1. Redis
    if (desc := await cache.get(photo_url)):
        logger.info(GREEN + f"Redis 命中: {photo_url}")
        return desc
    # 2. Postgres
    if (desc := await db.fetch_desc(photo_url)):
        logger.info(GREEN + f"PostgreSQL 命中: {photo_url}")
        await cache.set(photo_url, desc)
        return desc
    # 3. OpenAI (使用改进版的队列处理)
    desc = await describe_by_openai_with_queue(photo_url)
    # 注意：OpenAI客户端已经记录了成功日志，这里不需要重复记录
    await db.upsert_desc(photo_url, desc)
    await cache.set(photo_url, desc)
    return desc
