def parse_product_url(product_id, product_img_num=1):
    """
     根据商品ID和图片数量生成商品信息URL和图片URL。

     Args:
         product_id (str or int): 商品的ID。
         product_img_num (str or int): 需要生成的商品图片数量。

     Returns:
         dict: 包含商品信息URL ('product_url') 和商品图片URL列表 ('parse_product_urls') 的字典。
     """
    product_id = int(product_id)
    short_id = product_id // 100000

    basket = ''
    if short_id <= 3486:
        if 0 <= short_id <= 143:
            basket = '01'
        elif 144 <= short_id <= 287:
            basket = '02'
        elif 288 <= short_id <= 431:
            basket = '03'
        elif 432 <= short_id <= 719:
            basket = '04'
        elif 720 <= short_id <= 1007:
            basket = '05'
        elif 1008 <= short_id <= 1061:
            basket = '06'
        elif 1062 <= short_id <= 1115:
            basket = '07'
        elif 1116 <= short_id <= 1169:
            basket = '08'
        elif 1170 <= short_id <= 1313:
            basket = '09'
        elif 1314 <= short_id <= 1601:
            basket = '10'
        elif 1602 <= short_id <= 1655:
            basket = '11'
        elif 1656 <= short_id <= 1919:
            basket = '12'
        elif 1920 <= short_id <= 2045:
            basket = '13'
        elif 2046 <= short_id <= 2189:
            basket = '14'
        elif 2190 <= short_id <= 2405:
            basket = '15'
        elif 2406 <= short_id <= 2621:
            basket = '16'
        elif 2622 <= short_id <= 2837:
            basket = '17'
        elif 2838 <= short_id <= 3053:
            basket = '18'
        elif 3054 <= short_id <= 3270:
            basket = '19'
        elif 3271 <= short_id <= 3486:
            basket = '20'
    else:
        delta = short_id - 3487
        basket_num = 21 + (delta // 216)
        # 将数字格式化为两位数，不足的前面补零 (e.g., 21 -> '21', 9 -> '09')
        basket = str(basket_num).zfill(2)

    part = product_id // 1000

    # 使用 f-string 构建 URL
    product_url = f"https://basket-{basket}.wbbasket.ru/vol{short_id}/part{part}/{product_id}/info/ru/card.json"

    parse_product_urls = []
    # 确保 product_img_num 是整数类型，以便用于 range
    for i in range(1, int(product_img_num) + 1):
        image_url = f"https://basket-{basket}.wbbasket.ru/vol{short_id}/part{part}/{product_id}/images/big/{i}.webp"
        parse_product_urls.append(image_url)

    return {
        "parse_product_urls": parse_product_urls,
        "product_url": product_url
    }


nm_ids = [
"449105617",
"313067529",
"376736954",
"387577046",
"148846766",
"243143892",
"314009537",
"187549117",
"150705176",
"376742484",
"260270720",
"340516261",
"200145118",
"220345621",
"277629371",
"182689186",
"405601947",
"432853669",
"456726971",
"376738620",
"445850436",
"314971678",
"413296420",
"238588003",
"258146309",
"398434274",
"180930025",
"380603288",
"262068493",
"311770927",
"175006413",
"255986350",
"387508040",
"237359344",
"188299732",
"255974516",
"376759565",
"377219109",
"374128070",
"306752133",
"431549190",
"197515334",
"423359553",
"439596900",
"278622414",
"301440198",
"137745547",
"428907901",
"137745548",
"333909741",
"211622334",
"162380683",
"209964711",
"275059290",
"287270163",
"390769773",
"275058878",
"166745199",
"433123485",
"293663134",
"209215666",
"269844371",
"207013862",
"314316398",
"367441854",
"209215661",
"236548631",
"453132271",
"456595360",
"306123663",
"431991869",
"137743176",
"168384635",
"138904906",
"270892781",
"260874371",
"375423737",
"302830604",
"302445943",
"253272571",
"270891832",
"269844822",
"235226067",
"309717401",
"390503264",
"259449994",
"407688724",
"396423648",
"275058601",
"347592693",
"337720230",
"418004486",
"401897332",
"201846915",
"172363371",
"414848303",
"327303042",
"355437341",
"463311419",
"253061970",
"142912453",
"269844821",
"337906661",
"315338327",
"238562810",
"169754453",
"238563137",
"297327090",
"169754450",
"138856968",
"355450625",
"280200785",
"386505463",
"154171081",
"338596755",
"149718176",
"239008691",
"276572159",
"201935559",
"321598411",
"439559751",
"160197308",
"248886888",
"311878078",
"213831077",
"260369214",
"270892780",
"445859990",
"220508731",
"162914250",
"231081158",
"272081615",
"326889250",
"238562802",
"239367426",
"167734451",
"207405292",
"196101490",
"229672845",
"164693120",
"238536871",
"371705527",
"193422438",
"457896808",
"451222030",
"328153867",
"159137881",
"235321312",
"202101581",
"328153700",
"258855763",
"201931845",
"201935556",
"266136527",
"291116707",
"328125126",
"296124953",
"208269742",
"164670414",
"302770498",
"253136740",
"164671276",
"109300289",
"379695046",
"457896809",
"262324042",
"455090489",
"179644652",
"172223133",
"164671282",
"417032813",
"261661821",
"187066277",
"383481162",
"429471890",
"360414215",
"273753403",
"440611501",
"406965490",
"247584046",
"247579901",
"365836077",
"313937419",
"440619419",
"256821075",
"238917602",
"247580269",
"360663955",
"437861047",
"322184994",
"436026163",
"215073818",
"237846396",
"200567290",
"263767801",
"211119527",
"309934702",
"390995232",
"228474367",
"181344416",
"303728491",
"350903106",
"343870730",
"390406559",
"244264170",
"391253483",
"145122022",
"446905840",
"148670277",
"384814818",
"109020162",
"123825615",
"190422146",
"123825616",
"185009288",
"145122024",
"409992708",
"251889301",
"341541826",
"147349692",
"270775725",
"430012836",
"251895542",
"179572542",
"391245131",
"195283152",
"195283155",
"268696526",
"218732614",
"164595488",
"195283154",
"379317755",
"169716888",
"164595485",
"150636881",
"170852836",
"195283153",
"264535316",
"208206717",
"182632464",
"231726164",
"156738293",
"253870451",
"222463759",
"172608371",
"109793579",
"228378482",
"376325256",
"317860054",
"314338019",
"251631254",
"397113290",
"237885829",
"246031622",
"239458959",
"315922251",
"322946977",
"197285562",
"262363350",
"471220003",
"273100064",
"154906373",
"234143255",
"189205992",
"220697768",
"117911025",
"217883845",
"340634173",
"274397562",
"141097386",
"308063261",
"284236755",
"220891692",
"274397516",
"198085241",
"274397599",
"386474926",
"200585078",
"203573167",
"417797874",
"380996894",
"433123483",
"149935210",
"358126196",
"376511106",
"152666907",
"253816147",
"300303537",
"386300107",
"355604137",
"182277170",
"295332877",
"358850796",
"168488382",
"190103014",
"358929359",
"387327740",
"265970745",
"205172405",
"386300105",
"159973580",
"386300103",
"446128132",
"434442304",
"138735888",
"183886832",
"265970746",
"169026809",
"240398132",
"177911776",
"268444139",
"161454990",
"237127007",
"139645106",
"174014894",
"411779587",
"419172842",
"294539474",
"409832649",
"358913183",
"205733467",
"355606668",
"250937791",
"143123961",
"123833112",
"340987417",
"329505974",
"127672398",
"220752144",
"161454985",
"311130319",
"397058184",
"320834577",
"304977566",
"201238093",
"409910018",
"358892922",
"209870501",
"416816596",
"319839491",
"231882225",
"223152970",
"171614137",
"401841753",
"277157022",
"306671893",
"197689334",
"197689333",
"367532884",
"397706470",
"413282792",
"228755706",
"197689331",
"453427804",

]

for nm_id in nm_ids:
    img_url = parse_product_url(nm_id, 1)['parse_product_urls'][0]
    print(img_url)

