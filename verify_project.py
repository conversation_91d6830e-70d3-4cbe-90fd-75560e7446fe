#!/usr/bin/env python3
"""验证项目结构和代码正确性"""
import os
import sys
from pathlib import Path

def check_file_exists(filepath, description=""):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        print(f"✓ {filepath} {description}")
        return True
    else:
        print(f"❌ {filepath} 不存在 {description}")
        return False

def check_project_structure():
    """检查项目结构"""
    print("=== 检查项目结构 ===")
    
    required_files = [
        ("src/img_describer/__init__.py", "包初始化文件"),
        ("src/img_describer/config.py", "配置管理"),
        ("src/img_describer/logging.py", "日志配置"),
        ("src/img_describer/cache.py", "Redis 缓存"),
        ("src/img_describer/db.py", "PostgreSQL 数据库"),
        ("src/img_describer/openai_client.py", "OpenAI 客户端"),
        ("src/img_describer/service.py", "核心业务逻辑"),
        ("src/img_describer/api.py", "FastAPI 接口"),
        ("src/img_describer/cli.py", "命令行接口"),
        ("tests/test_service.py", "测试文件"),
        ("Dockerfile", "Docker 镜像配置"),
        ("docker-compose.yml", "Docker 编排配置"),
        (".env.example", "环境变量示例"),
        ("requirements.txt", "Python 依赖"),
        ("pyproject.toml", "项目配置"),
        ("README.md", "项目说明"),
        ("run_api.py", "API 启动脚本"),
        ("run_cli.py", "CLI 启动脚本"),
    ]
    
    all_exist = True
    for filepath, description in required_files:
        if not check_file_exists(filepath, description):
            all_exist = False
    
    return all_exist

def check_python_syntax():
    """检查 Python 文件语法"""
    print("\n=== 检查 Python 语法 ===")
    
    python_files = []
    for root, dirs, files in os.walk("src"):
        for file in files:
            if file.endswith(".py"):
                python_files.append(os.path.join(root, file))
    
    # 添加根目录的 Python 文件
    for file in ["run_api.py", "run_cli.py", "test_imports.py", "test_redis_config.py", "test_redis_url_builder.py"]:
        if os.path.exists(file):
            python_files.append(file)
    
    all_valid = True
    for py_file in python_files:
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                compile(f.read(), py_file, 'exec')
            print(f"✓ {py_file} 语法正确")
        except SyntaxError as e:
            print(f"❌ {py_file} 语法错误: {e}")
            all_valid = False
        except Exception as e:
            print(f"❌ {py_file} 检查失败: {e}")
            all_valid = False
    
    return all_valid

def main():
    """主函数"""
    print("🔍 开始验证项目...")
    
    structure_ok = check_project_structure()
    syntax_ok = check_python_syntax()
    
    print("\n=== 验证结果 ===")
    if structure_ok and syntax_ok:
        print("🎉 项目验证通过！所有文件结构和语法都正确。")
        print("\n📋 下一步操作:")
        print("1. 复制 .env.example 为 .env 并配置你的 OpenAI 凭证")
        print("2. 运行 'docker-compose up --build' 启动服务")
        print("3. 或者安装依赖后使用 'python run_api.py' 本地运行")
        return True
    else:
        print("❌ 项目验证失败，请检查上述错误。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
