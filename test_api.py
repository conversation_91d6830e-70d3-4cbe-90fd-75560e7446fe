#!/usr/bin/env python3
"""测试 API 接口"""
import requests
import json
import time

def test_api():
    """测试 API 接口"""
    print("🚀 测试 API 接口...")
    
    # 测试图片URL
    test_url = "https://basket-25.wbbasket.ru/vol4491/part449105/449105617/images/big/1.webp"
    api_url = f"http://localhost:8000/describe?url={test_url}"
    
    print(f"📋 测试URL: {test_url}")
    print(f"🔗 API URL: {api_url}")
    
    try:
        print("📡 发送请求...")
        start_time = time.time()
        
        response = requests.get(api_url, timeout=30)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  响应时间: {duration:.2f}秒")
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API 调用成功！")
            print(f"🖼️  图片URL: {result.get('url', 'N/A')}")
            print(f"📝 描述内容: {result.get('description', 'N/A')}")
            return True
        else:
            print(f"❌ API 调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_api_docs():
    """测试 API 文档"""
    print("\n🔍 测试 API 文档...")
    
    try:
        response = requests.get("http://localhost:8000/docs", timeout=10)
        if response.status_code == 200:
            print("✅ API 文档可访问: http://localhost:8000/docs")
            return True
        else:
            print(f"❌ API 文档访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API 文档访问异常: {e}")
        return False

if __name__ == "__main__":
    print("🧪 开始 API 测试...")
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    # 测试 API 文档
    docs_ok = test_api_docs()
    
    # 测试 API 功能
    api_ok = test_api()
    
    print(f"\n{'='*50}")
    print("📊 API 测试结果:")
    print(f"API 文档: {'✅' if docs_ok else '❌'}")
    print(f"API 功能: {'✅' if api_ok else '❌'}")
    
    if docs_ok and api_ok:
        print("\n🎉 所有 API 测试通过！")
        print("🌐 您可以访问以下地址:")
        print("  - API 文档: http://localhost:8000/docs")
        print("  - API 接口: http://localhost:8000/describe?url=图片URL")
    else:
        print("\n❌ 部分 API 测试失败")
