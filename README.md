# Say Img Description

使用 OpenAI Vision 生成中文图片描述的微服务，支持 Redis 缓存与 PostgreSQL 持久化，提供 HTTP API 与 CLI。现已升级为基于 Consul 的微服务架构，支持服务发现、负载均衡和动态路由。

## 🚀 架构概览

### 传统架构 vs Consul 微服务架构

**传统架构**: `客户端 -> 微服务:8000/8001/8002...`

**Consul 架构**: `客户端 -> Nginx网关:80 -> Consul服务发现 -> 微服务:8000`

## 🎯 快速开始

### 方式一：分离部署架构（推荐生产环境）

#### 1.1 部署基础设施服务（主服务）

```bash
# 1. 启动基础设施服务（Consul、Nginx、Redis、PostgreSQL）
docker-compose -f docker-compose.infrastructure.yml up -d

# 2. 验证基础设施服务
curl http://localhost:8500/v1/status/leader  # Consul
curl http://localhost/gateway/status         # Nginx网关
```

#### 1.2 部署业务服务

```bash
# 1. 复制业务服务环境配置
cp .env.business.example .env

# 2. 编辑环境变量（必须设置 OPENAI_API_KEY）
nano .env

# 3. 启动业务服务
docker-compose -f docker-compose.business.yml up -d

# 4. 验证业务服务
curl http://localhost/api/say-img-description/health
curl "http://localhost/api/say-img-description/describe?url=IMAGE_URL"
```

### 方式二：一体化部署（Consul 微服务架构）

```bash
# 1. 复制 Consul 环境配置
cp .env.consul.example .env

# 2. 编辑环境变量（必须设置 OPENAI_API_KEY）
nano .env

# 3. 启动完整的 Consul 微服务架构
docker-compose -f docker-compose.consul.yml up -d

# 4. 验证部署
python verify_consul_deployment.py

# 5. 访问服务
# - API 网关: http://localhost
# - Consul UI: http://localhost:8500
# - 图片描述: http://localhost/api/say-img-description/describe?url=IMAGE_URL
```

### 方式三：传统架构（推荐开发环境）

```bash
# 1. 复制配置文件并编辑
cp .env.example .env
# 编辑 .env 文件，填入你的 OpenAI API 凭证

# 2. 启动传统架构服务
docker-compose up --build

# 3. 访问服务
# - API 文档: http://localhost:8000/docs
# - 图片描述: http://localhost:8000/describe?url=IMAGE_URL
```

### 方式四：本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
cp .env.example .env
# 编辑 .env 文件

# 启动 API 服务
python run_api.py

# 或者使用 CLI
python run_cli.py "https://example.com/image.jpg"
```

## 🔧 服务管理

### 分离部署架构服务管理

#### 基础设施服务管理

```bash
# 查看基础设施服务状态
docker-compose -f docker-compose.infrastructure.yml ps

# 查看基础设施服务日志
docker-compose -f docker-compose.infrastructure.yml logs consul
docker-compose -f docker-compose.infrastructure.yml logs nginx

# 重启基础设施服务
docker-compose -f docker-compose.infrastructure.yml restart consul

# 停止基础设施服务
docker-compose -f docker-compose.infrastructure.yml down

# 完全重置基础设施环境
docker-compose -f docker-compose.infrastructure.yml down -v
```

#### 业务服务管理

```bash
# 查看业务服务状态
docker-compose -f docker-compose.business.yml ps

# 查看业务服务日志
docker-compose -f docker-compose.business.yml logs say-img-description

# 扩展业务服务实例（自动负载均衡）
docker-compose -f docker-compose.business.yml up -d --scale say-img-description=3

# 重启业务服务
docker-compose -f docker-compose.business.yml restart say-img-description

# 停止业务服务
docker-compose -f docker-compose.business.yml down

# 更新业务服务
docker-compose -f docker-compose.business.yml build
docker-compose -f docker-compose.business.yml up -d
```

### 一体化部署架构服务管理

```bash
# 查看服务状态
docker-compose -f docker-compose.consul.yml ps

# 查看服务日志
docker-compose -f docker-compose.consul.yml logs say-img-description

# 扩展服务实例（自动负载均衡）
docker-compose -f docker-compose.consul.yml up -d --scale say-img-description=3

# 停止服务
docker-compose -f docker-compose.consul.yml down

# 完全重置环境
docker-compose -f docker-compose.consul.yml down -v
```

### 传统架构服务管理

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs api

# 停止服务
docker-compose down
```

## 🏗️ 部署架构说明

### 分离部署架构（推荐）

**优势**:
- 基础设施和业务服务独立部署和维护
- 不同业务服务可以使用不同的技术栈
- 便于团队协作和服务扩展
- 支持跨主机部署

**适用场景**:
- 生产环境
- 多团队协作
- 微服务架构
- 需要独立扩展的场景

### 一体化部署架构

**优势**:
- 部署简单，一键启动
- 适合单体应用迁移到微服务
- 开发和测试环境友好

**适用场景**:
- 开发和测试环境
- 小型项目
- 快速原型验证

### 传统架构

**优势**:
- 最简单的部署方式
- 适合单服务应用
- 资源占用少

**适用场景**:
- 本地开发
- 单服务应用
- 资源受限环境

## 📡 API 使用

### 分离部署和一体化部署 API 端点

- **API 网关**: http://localhost
- **服务发现**: http://localhost/api/services
- **网关状态**: http://localhost/gateway/status
- **Consul UI**: http://localhost:8500
- **图片描述服务**:
  - 健康检查: http://localhost/api/say-img-description/health
  - 服务信息: http://localhost/api/say-img-description/info
  - 图片描述: http://localhost/api/say-img-description/describe?url=图片URL

### 传统架构 API 端点

- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **图片描述**: http://localhost:8000/describe?url=图片URL

### API 使用示例

```bash
# 测试图片描述功能
curl "http://localhost/api/say-img-description/describe?url=https://via.placeholder.com/300x200.png?text=Test+Image"

# 检查服务健康状态
curl http://localhost/api/say-img-description/health

# 查看所有注册的服务
curl http://localhost/api/services

# 查看网关状态
curl http://localhost/gateway/status
```

## 🔌 新业务服务接入

### 快速接入指南

如果你有新的业务服务需要接入到现有的微服务架构中，请参考详细的接入指导文档：

📖 **[业务服务接入指导文档](./BUSINESS_SERVICE_INTEGRATION_GUIDE.md)**

### 接入步骤概览

1. **准备工作**
   ```bash
   # 复制模板文件
   cp business-service-template.yml docker-compose.yml
   cp .env.business.example .env
   ```

2. **修改配置**
   - 更新服务名称和配置
   - 设置环境变量
   - 配置数据库连接

3. **部署服务**
   ```bash
   # 确保基础设施服务正在运行
   docker-compose -f docker-compose.infrastructure.yml ps

   # 启动业务服务
   docker-compose up -d
   ```

4. **验证接入**
   ```bash
   # 检查服务注册
   curl http://localhost:8500/v1/catalog/service/your-service-name

   # 通过网关访问
   curl http://localhost/api/your-service-name/health
   ```

### 支持的业务服务类型

- **API 服务**: RESTful API、GraphQL API
- **数据处理服务**: 批处理、流处理
- **AI/ML 服务**: 机器学习推理、图像处理
- **业务逻辑服务**: 用户管理、订单处理、支付服务
- **第三方集成服务**: 外部 API 集成、消息队列处理

## 🏗️ 配置说明

### 分离部署架构配置

#### 基础设施服务配置

基础设施服务使用 `docker-compose.infrastructure.yml`，包含：
- **Consul**: 服务发现和配置管理
- **Nginx**: API 网关和负载均衡
- **Redis**: 缓存服务
- **PostgreSQL**: 数据库服务

#### 业务服务配置

业务服务使用 `.env.business.example` 模板：

```env
# Consul 服务发现配置
CONSUL_HOST=consul
CONSUL_PORT=8500

# 服务配置
SERVICE_NAME=say-img-description
SERVICE_PORT=8000
SERVICE_TAGS=api,image,ai
ENVIRONMENT=production

# 数据库连接配置
DATABASE_URL=****************************************/mydb
REDIS_URL=redis://redis:6379/0

# OpenAI 配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4-vision-preview
```

### 一体化部署架构配置

使用 `.env.consul.example` 模板：

```env
# Consul 配置
CONSUL_HOST=consul
CONSUL_PORT=8500

# 服务配置
SERVICE_NAME=say-img-description
SERVICE_PORT=8000
SERVICE_TAGS=api,image,ai
ENVIRONMENT=production

# OpenAI 配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4-vision-preview

# 数据库配置
DATABASE_URL=****************************************/mydb
REDIS_URL=redis://redis:6379/0
```

### 传统架构 Redis 配置

支持两种配置方式：

**方式1: 分别配置各项参数**
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password  # 可选，留空表示无密码
REDIS_DB=5
```

**方式2: 使用完整的 Redis URL**
```env
# 无密码
REDIS_URL=redis://localhost:6379/5

# 有密码
REDIS_URL=redis://:your_password@localhost:6379/5

# 外部 Redis 服务
REDIS_URL=redis://:password@your-redis-host:6379/5
```

如果同时设置了 `REDIS_URL` 和其他参数，会优先使用 `REDIS_URL`。

## 🏢 新微服务开发

### 快速创建新微服务

```bash
# 使用脚本创建新微服务
./create_microservice.sh user-service

# 进入新服务目录
cd user-service

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 实现业务逻辑
# 编辑 src/user_service/service.py
# 编辑 src/user_service/api.py

# 启动服务
python run_consul_api.py
```

### 微服务开发规范

每个新微服务必须实现：

1. **健康检查端点**: `GET /health`
2. **服务信息端点**: `GET /info`
3. **Consul 服务注册**: 启动时自动注册
4. **环境变量配置**: 使用标准的环境变量
5. **统一端口**: 所有服务使用 8000 端口

### 添加到 Docker Compose

在 `docker-compose.consul.yml` 中添加新服务：

```yaml
your-service:
  build: ./your-service
  environment:
    - CONSUL_HOST=consul
    - SERVICE_NAME=your-service
    - SERVICE_PORT=8000
    - SERVICE_TAGS=api,your-domain
  depends_on:
    consul:
      condition: service_healthy
  networks:
    - microservices
```

## 📁 项目结构

```
say_img_description/
├── src/img_describer/           # 主要代码
│   ├── __init__.py              # 包初始化
│   ├── config.py                # 配置管理
│   ├── logging.py               # 日志配置
│   ├── cache.py                 # Redis 缓存
│   ├── db.py                    # PostgreSQL 数据库
│   ├── openai_client.py         # OpenAI 客户端
│   ├── service.py               # 核心业务逻辑
│   ├── api.py                   # FastAPI 接口（支持 Consul）
│   ├── cli.py                   # 命令行接口
│   ├── consul_service.py        # Consul 服务管理
│   └── service_client.py        # 微服务间调用客户端
├── nginx/                       # Nginx 配置
│   ├── nginx.conf               # 基础配置
│   └── consul-ui.conf           # Consul UI 代理
├── consul-template/             # Consul Template
│   └── nginx.conf.tpl           # 动态配置模板
├── tests/                       # 测试文件
├── run_api.py                   # 传统模式启动脚本
├── run_consul_api.py            # Consul 模式启动脚本
├── run_cli.py                   # CLI 启动脚本
├── docker-compose.yml           # 传统架构 Docker 编排
├── docker-compose.consul.yml    # Consul 架构 Docker 编排
├── create_microservice.sh       # 新微服务创建脚本
├── verify_consul_deployment.py  # 部署验证脚本
├── .env.example                 # 传统模式环境变量模板
├── .env.consul.example          # Consul 模式环境变量模板
├── README.consul.md             # Consul 架构详细文档
└── Dockerfile                   # Docker 镜像
```

## 🔍 监控和调试

### Consul UI 监控

访问 http://localhost:8500 查看：
- 服务注册状态
- 健康检查结果
- 服务实例列表
- KV 存储

### 服务健康检查

```bash
# 检查特定服务健康状态
curl http://localhost/api/img-describer/health

# 查看 Consul 中的健康检查
curl http://localhost:8500/v1/health/service/img-describer

# 查看所有服务状态
curl http://localhost/api/services
```

### 日志查看

```bash
# 实时查看服务日志
docker-compose -f docker-compose.consul.yml logs -f img-describer

# 查看网关日志
docker-compose -f docker-compose.consul.yml logs -f nginx

# 查看 Consul 日志
docker-compose -f docker-compose.consul.yml logs -f consul

# 查看 consul-template 日志
docker-compose -f docker-compose.consul.yml logs -f consul-template
```

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 服务无法注册到 Consul

```bash
# 检查 Consul 连接
docker-compose -f docker-compose.consul.yml logs consul

# 检查服务环境变量
docker-compose -f docker-compose.consul.yml exec img-describer env | grep CONSUL

# 验证网络连接
docker-compose -f docker-compose.consul.yml exec img-describer ping consul
```

#### 2. Nginx 无法路由到服务

```bash
# 检查 consul-template 日志
docker-compose -f docker-compose.consul.yml logs consul-template

# 检查生成的 Nginx 配置
docker-compose -f docker-compose.consul.yml exec nginx cat /etc/nginx/conf.d/services.conf

# 重启 consul-template
docker-compose -f docker-compose.consul.yml restart consul-template
```

#### 3. 服务健康检查失败

```bash
# 直接测试健康检查端点
docker-compose -f docker-compose.consul.yml exec img-describer wget -qO- http://localhost:8000/health

# 检查服务启动日志
docker-compose -f docker-compose.consul.yml logs img-describer

# 检查端口是否正确监听
docker-compose -f docker-compose.consul.yml exec img-describer netstat -tlnp
```

#### 4. 完全重置环境

```bash
# 停止所有服务并删除数据
docker-compose -f docker-compose.consul.yml down -v

# 清理 Docker 系统
docker system prune -f

# 重新启动
docker-compose -f docker-compose.consul.yml up -d
```

### 部署验证

使用自动化验证脚本：

```bash
# 运行完整的部署验证
python verify_consul_deployment.py

# 手动验证关键端点
curl http://localhost/gateway/status
curl http://localhost/api/services
curl http://localhost/api/img-describer/health
```

## ⚡ 性能优化

### 负载均衡策略

默认使用 `least_conn` 策略，可在 `consul-template/nginx.conf.tpl` 中修改：

```nginx
upstream service_backend {
    # 可选策略：ip_hash, least_conn, random
    least_conn;
    server backend1:8000 weight=3;
    server backend2:8000 weight=1;
}
```

### 缓存优化

```env
# 调整缓存配置
CACHE_TTL=3600          # 缓存过期时间
CACHE_MAX_SIZE=1000     # 最大缓存条目数
```

### 并发控制

```env
# 调整并发参数
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30
MAX_RETRIES=3
```

### 服务扩展

```bash
# 水平扩展服务实例
docker-compose -f docker-compose.consul.yml up -d --scale img-describer=5

# Consul 会自动发现新实例，Nginx 自动负载均衡
```

## 🔒 安全考虑

### 生产环境安全配置

1. **Consul 安全**:
   ```bash
   # 启用 ACL（访问控制列表）
   # 配置 TLS 加密
   # 设置防火墙规则
   ```

2. **API 安全**:
   ```env
   # 添加 API 密钥认证
   API_KEY=your_secure_api_key

   # 配置 CORS
   CORS_ORIGINS=https://yourdomain.com
   ```

3. **网络安全**:
   ```yaml
   # 在 docker-compose.consul.yml 中限制网络访问
   networks:
     microservices:
       driver: bridge
       internal: true  # 内部网络
   ```

### 环境变量安全

```bash
# 使用 Docker secrets 管理敏感信息
echo "your_openai_api_key" | docker secret create openai_key -

# 或使用外部密钥管理系统
# - HashiCorp Vault
# - AWS Secrets Manager
# - Azure Key Vault
```

## 📊 监控和指标

### 添加 Prometheus 监控

```yaml
# 在 docker-compose.consul.yml 中添加
prometheus:
  image: prom/prometheus
  ports:
    - "9090:9090"
  volumes:
    - ./prometheus.yml:/etc/prometheus/prometheus.yml

grafana:
  image: grafana/grafana
  ports:
    - "3000:3000"
  environment:
    - GF_SECURITY_ADMIN_PASSWORD=admin
```

### 日志聚合

```yaml
# 添加 ELK Stack
elasticsearch:
  image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0

logstash:
  image: docker.elastic.co/logstash/logstash:8.8.0

kibana:
  image: docker.elastic.co/kibana/kibana:8.8.0
  ports:
    - "5601:5601"
```

## 🌟 扩展功能

### 服务网格（Consul Connect）

```bash
# 启用 Consul Connect 进行服务间 TLS 通信
consul connect envoy -sidecar-for img-describer
```

### 配置管理（Consul KV）

```bash
# 使用 Consul KV 存储配置
consul kv put config/img-describer/model "gpt-4-vision-preview"
consul kv put config/img-describer/timeout "30"
```

### 分布式锁

```python
# 在代码中使用 Consul 分布式锁
from consul import Consul

consul = Consul()
session = consul.session.create()
acquired = consul.kv.put('locks/image-processing', 'locked', acquire=session)
```

## 📚 相关文档

- [README.consul.md](README.consul.md) - Consul 架构详细文档
- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [Consul 文档](https://www.consul.io/docs)
- [Nginx 文档](https://nginx.org/en/docs/)
- [Docker Compose 文档](https://docs.docker.com/compose/)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如有问题，请：

1. 查看 [故障排除](#-故障排除) 部分
2. 检查 [Issues](https://github.com/your-repo/issues) 中的已知问题
3. 运行 `python verify_consul_deployment.py` 进行自动诊断
4. 查看服务日志和 Consul UI
5. 创建新的 Issue 描述问题

### 常用调试命令

```bash
# 快速健康检查
curl http://localhost/gateway/status
curl http://localhost/api/img-describer/health

# 查看服务注册状态
curl http://localhost:8500/v1/agent/services

# 测试图片描述功能
curl "http://localhost/api/img-describer/describe?url=https://via.placeholder.com/300x200.png?text=Test"

# 查看所有容器状态
docker-compose -f docker-compose.consul.yml ps

# 重启特定服务
docker-compose -f docker-compose.consul.yml restart img-describer
```

---

**🎉 现在你已经拥有了一个完整的基于 Consul 的微服务架构！可以轻松扩展和管理多个微服务。**
