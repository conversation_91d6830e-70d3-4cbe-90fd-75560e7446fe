# Redis 配置示例文件
# 复制到 .env 文件中使用

# ========================================
# 场景1: 本地开发，无密码 Redis
# ========================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=5
REDIS_URL=

# ========================================
# 场景2: 本地开发，有密码 Redis
# ========================================
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=myredispassword
# REDIS_DB=5
# REDIS_URL=

# ========================================
# 场景3: 使用完整 URL（本地无密码）
# ========================================
# REDIS_HOST=
# REDIS_PORT=
# REDIS_PASSWORD=
# REDIS_DB=
# REDIS_URL=redis://localhost:6379/5

# ========================================
# 场景4: 使用完整 URL（本地有密码）
# ========================================
# REDIS_HOST=
# REDIS_PORT=
# REDIS_PASSWORD=
# REDIS_DB=
# REDIS_URL=redis://:myredispassword@localhost:6379/5

# ========================================
# 场景5: 外部 Redis 服务（如阿里云、腾讯云）
# ========================================
# REDIS_HOST=
# REDIS_PORT=
# REDIS_PASSWORD=
# REDIS_DB=
# REDIS_URL=redis://:<EMAIL>:6379/0

# ========================================
# 场景6: Docker Compose 中的 Redis（无密码）
# ========================================
# REDIS_HOST=redis
# REDIS_PORT=6379
# REDIS_PASSWORD=
# REDIS_DB=5
# REDIS_URL=

# ========================================
# 场景7: Docker Compose 中的 Redis（有密码）
# ========================================
# REDIS_HOST=redis
# REDIS_PORT=6379
# REDIS_PASSWORD=dockerredispass
# REDIS_DB=5
# REDIS_URL=

# 其他配置保持不变...
PG_USER=user
PG_PASSWORD=password
PG_DB=mydb
PG_HOST=postgres
PG_PORT=5432

OPENAI_CREDENTIALS=[{"api_key":"sk-...","base_url":"https://api.openai.com/v1","model":"gpt-4o-mini"}]
