#!/usr/bin/env python3
"""独立测试403错误重试行为 - 不依赖其他模块"""
import json
import random
import asyncio
import time
from typing import Dict, List
from unittest.mock import AsyncMock, patch, MagicMock

# 模拟openai模块
class MockRateLimitError(Exception):
    """模拟RateLimitError"""
    pass

class MockBadRequestError(Exception):
    """模拟403错误"""
    def __init__(self, message, response=None):
        super().__init__(message)
        self.response = response

class MockAsyncOpenAI:
    """模拟AsyncOpenAI客户端"""
    def __init__(self, api_key, base_url, timeout):
        self.api_key = api_key
        self.base_url = base_url
        self.timeout = timeout
        self.chat = MockChat()

class MockChat:
    """模拟chat对象"""
    def __init__(self):
        self.completions = MockCompletions()

class MockCompletions:
    """模拟completions对象"""
    async def create(self, **kwargs):
        # 模拟403错误
        if "disabled_key" in kwargs.get('model', ''):
            raise MockBadRequestError("Error code: 403 - {'error': {'message': 'User disabled. (request id: test)', 'type': 'upstream_error', 'param': '403', 'code': 'bad_response_status_code'}}")
        
        # 模拟429错误
        if "rate_limit_key" in kwargs.get('model', ''):
            raise MockRateLimitError("Rate limit exceeded")
        
        # 模拟成功响应
        return MockResponse()

class MockResponse:
    """模拟响应对象"""
    def __init__(self):
        self.choices = [MockChoice()]

class MockChoice:
    """模拟choice对象"""
    def __init__(self):
        self.message = MockMessage()

class MockMessage:
    """模拟message对象"""
    def __init__(self):
        self.content = "这是一张测试图片的描述"

# 模拟tenacity重试装饰器
def mock_retry(stop, wait, retry, reraise=True):
    """模拟重试装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            max_attempts = 5  # stop_after_attempt(5)
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    # 检查是否应该重试
                    should_retry = False
                    if hasattr(retry, 'predicate'):
                        should_retry = retry.predicate(e)
                    elif hasattr(retry, '__call__'):
                        should_retry = retry(e)
                    
                    if should_retry and attempt < max_attempts - 1:
                        print(f"   🔄 重试第{attempt + 1}次，异常: {type(e).__name__}: {str(e)[:50]}...")
                        await asyncio.sleep(1)  # 简化等待时间
                        continue
                    else:
                        print(f"   ❌ 不重试或达到最大重试次数，异常: {type(e).__name__}: {str(e)[:50]}...")
                        raise
            
        return wrapper
    return decorator

# 模拟重试条件
def mock_retry_if_exception_type(exception_type):
    """模拟重试条件"""
    def predicate(exception):
        result = isinstance(exception, exception_type)
        print(f"   🔍 检查是否重试: {type(exception).__name__} -> {result}")
        return result
    
    predicate.predicate = predicate
    return predicate

class RateLimitedError(Exception):
    """自定义RateLimitedError"""
    pass

# 模拟OpenAI客户端函数
@mock_retry(
    stop="stop_after_attempt(5)", 
    wait="wait_exponential", 
    retry=mock_retry_if_exception_type(RateLimitedError)
)
async def mock_describe_by_openai_improved(photo_url: str, test_scenario: str = "normal") -> str:
    """模拟改进版OpenAI客户端"""
    print(f"   📞 调用改进版客户端，场景: {test_scenario}")
    
    # 模拟不同的测试场景
    if test_scenario == "403_error":
        client = MockAsyncOpenAI("test_key", "test_url", 30)
        try:
            await client.chat.completions.create(model="disabled_key")
        except MockBadRequestError as e:
            print(f"   🚫 捕获到403错误: {str(e)[:100]}...")
            raise  # 403错误不转换，直接抛出
    elif test_scenario == "429_error":
        client = MockAsyncOpenAI("test_key", "test_url", 30)
        try:
            await client.chat.completions.create(model="rate_limit_key")
        except MockRateLimitError as e:
            print(f"   🚫 捕获到429错误，转换为RateLimitedError")
            raise RateLimitedError(f"Rate limit exceeded: {e}")
    else:
        # 正常情况
        client = MockAsyncOpenAI("test_key", "test_url", 30)
        response = await client.chat.completions.create(model="normal_key")
        return response.choices[0].message.content

@mock_retry(
    stop="stop_after_attempt(5)", 
    wait="wait_fixed(10)", 
    retry=mock_retry_if_exception_type(RateLimitedError)
)
async def mock_describe_by_openai(photo_url: str, test_scenario: str = "normal") -> str:
    """模拟原始OpenAI客户端"""
    print(f"   📞 调用原始客户端，场景: {test_scenario}")
    
    # 模拟不同的测试场景
    if test_scenario == "403_error":
        client = MockAsyncOpenAI("test_key", "test_url", 30)
        try:
            await client.chat.completions.create(model="disabled_key")
        except MockBadRequestError as e:
            print(f"   🚫 捕获到403错误: {str(e)[:100]}...")
            raise  # 403错误不转换，直接抛出
    elif test_scenario == "429_error":
        client = MockAsyncOpenAI("test_key", "test_url", 30)
        try:
            await client.chat.completions.create(model="rate_limit_key")
        except MockRateLimitError as e:
            print(f"   🚫 捕获到429错误，转换为RateLimitedError")
            raise RateLimitedError(f"Rate limit exceeded: {e}")
    else:
        # 正常情况
        client = MockAsyncOpenAI("test_key", "test_url", 30)
        response = await client.chat.completions.create(model="normal_key")
        return response.choices[0].message.content

async def test_retry_behavior():
    """测试重试行为"""
    print("🔍 测试重试行为...")
    print("="*80)
    
    test_url = "https://example.com/test.jpg"
    
    # 测试场景
    scenarios = [
        ("正常请求", "normal"),
        ("403错误", "403_error"),
        ("429错误", "429_error")
    ]
    
    for scenario_name, scenario_code in scenarios:
        print(f"\n🧪 测试场景: {scenario_name}")
        print("-" * 60)
        
        # 测试改进版客户端
        print(f"📋 改进版客户端:")
        start_time = time.time()
        try:
            result = await mock_describe_by_openai_improved(test_url, scenario_code)
            end_time = time.time()
            print(f"   ✅ 成功! 耗时: {end_time - start_time:.2f}秒")
            print(f"   📝 结果: {result}")
        except Exception as e:
            end_time = time.time()
            print(f"   ❌ 失败! 耗时: {end_time - start_time:.2f}秒")
            print(f"   🚫 最终异常: {type(e).__name__}: {str(e)[:100]}...")
        
        # 测试原始客户端
        print(f"\n📋 原始客户端:")
        start_time = time.time()
        try:
            result = await mock_describe_by_openai(test_url, scenario_code)
            end_time = time.time()
            print(f"   ✅ 成功! 耗时: {end_time - start_time:.2f}秒")
            print(f"   📝 结果: {result}")
        except Exception as e:
            end_time = time.time()
            print(f"   ❌ 失败! 耗时: {end_time - start_time:.2f}秒")
            print(f"   🚫 最终异常: {type(e).__name__}: {str(e)[:100]}...")

async def main():
    """主函数"""
    print("🚀 开始403错误重试行为独立测试...")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("📋 测试说明:")
    print("   - 模拟OpenAI客户端的行为")
    print("   - 测试不同错误场景的重试机制")
    print("   - 验证403错误是否会触发重试")
    print()
    
    await test_retry_behavior()
    
    print("\n" + "="*80)
    print("📊 测试结论:")
    print("   ✅ 429错误 (RateLimitError) 会触发重试机制")
    print("   ❌ 403错误 (其他Exception) 不会触发重试机制")
    print("   🚨 这确认了403错误会立即失败，不会重试")
    print("   💡 需要专门处理403错误并切换API密钥")

if __name__ == "__main__":
    asyncio.run(main())
