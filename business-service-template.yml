# 业务服务 Docker Compose 模板
# 复制此文件并根据具体业务服务进行修改

version: '3.9'

networks:
  microservices:
    # 连接到外部的基础设施网络
    name: microservices
    external: true

services:
  # 替换为你的业务服务名称
  your-business-service:
    # 构建配置
    build: 
      context: .
      dockerfile: Dockerfile
    
    # 容器配置
    container_name: your-business-service-server
    hostname: your-business-service
    
    # 环境变量配置
    environment:
      # Consul 服务发现配置
      - CONSUL_HOST=${CONSUL_HOST:-consul}
      - CONSUL_PORT=${CONSUL_PORT:-8500}
      
      # 服务基本配置
      - SERVICE_NAME=your-business-service
      - SERVICE_PORT=${SERVICE_PORT:-8000}
      - SERVICE_TAGS=${SERVICE_TAGS:-api,business}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      
      # 数据库连接配置
      - DATABASE_URL=${DATABASE_URL:-****************************************/mydb}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
      
      # 应用配置
      - HOST=0.0.0.0
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      
      # 业务服务特定的环境变量
      # 在这里添加你的业务服务需要的环境变量
      # - YOUR_API_KEY=${YOUR_API_KEY}
      # - YOUR_CONFIG=${YOUR_CONFIG}
    
    # 环境变量文件
    env_file:
      - .env
    
    # 网络配置
    networks:
      - microservices
    
    # 健康检查
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:${SERVICE_PORT:-8000}/health"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    # 重启策略
    restart: unless-stopped
    
    # 启动命令
    command: ["python", "run_consul_api.py"]
    
    # 可选：端口映射（如果需要直接访问）
    # ports:
    #   - "${SERVICE_PORT:-8000}:${SERVICE_PORT:-8000}"
    
    # 可选：数据卷挂载
    # volumes:
    #   - ./data:/app/data
    #   - ./logs:/app/logs
    
    # 可选：依赖服务（如果有独立的数据库或缓存）
    # depends_on:
    #   - your-business-postgres
    #   - your-business-redis

  # 可选：业务服务专用的 PostgreSQL 数据库
  # your-business-postgres:
  #   image: postgres:16-alpine
  #   container_name: your-business-postgres
  #   hostname: your-business-postgres
  #   environment:
  #     POSTGRES_USER: ${DB_USER:-business_user}
  #     POSTGRES_PASSWORD: ${DB_PASSWORD:-business_password}
  #     POSTGRES_DB: ${DB_NAME:-business_db}
  #     POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
  #   volumes:
  #     - your_business_postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - microservices
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-business_user} -d ${DB_NAME:-business_db}"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5
  #     start_period: 10s
  #   restart: unless-stopped

  # 可选：业务服务专用的 Redis 缓存
  # your-business-redis:
  #   image: redis:7-alpine
  #   container_name: your-business-redis
  #   hostname: your-business-redis
  #   volumes:
  #     - your_business_redis_data:/data
  #   networks:
  #     - microservices
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 3
  #     start_period: 5s
  #   restart: unless-stopped
  #   # 如果需要密码保护
  #   # command: redis-server --requirepass your_redis_password

# 可选：业务服务专用数据卷
# volumes:
#   your_business_postgres_data:
#     name: your_business_postgres_data
#   your_business_redis_data:
#     name: your_business_redis_data
