#!/usr/bin/env python3
"""测试图片URL描述功能"""
import sys
import os
import asyncio
import time
from typing import List

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 测试图片URL列表
TEST_URLS = [
    "https://basket-25.wbbasket.ru/vol4491/part449105/449105617/images/big/1.webp",
    "https://basket-19.wbbasket.ru/vol3130/part313067/313067529/images/big/1.webp",
    "https://basket-22.wbbasket.ru/vol3767/part376736/376736954/images/big/1.webp",
    "https://basket-22.wbbasket.ru/vol3875/part387577/387577046/images/big/1.webp",
    "https://basket-10.wbbasket.ru/vol1488/part148846/148846766/images/big/1.webp",
    "https://basket-16.wbbasket.ru/vol2431/part243143/243143892/images/big/1.webp",
    "https://basket-19.wbbasket.ru/vol3140/part314009/314009537/images/big/1.webp",
    "https://basket-12.wbbasket.ru/vol1875/part187549/187549117/images/big/1.webp",
    "https://basket-10.wbbasket.ru/vol1507/part150705/150705176/images/big/1.webp",
    "https://basket-22.wbbasket.ru/vol3767/part376742/376742484/images/big/1.webp",
]

async def test_single_url(url: str, index: int) -> dict:
    """测试单个URL"""
    print(f"\n{'='*60}")
    print(f"测试 {index+1}: {url}")
    print('='*60)
    
    start_time = time.time()
    
    try:
        from img_describer.service import describe_image
        
        # 调用描述服务
        description = await describe_image(url)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 成功 (耗时: {duration:.2f}秒)")
        print(f"📝 描述: {description}")
        
        return {
            "url": url,
            "success": True,
            "description": description,
            "duration": duration,
            "error": None
        }
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"❌ 失败 (耗时: {duration:.2f}秒)")
        print(f"🚫 错误: {str(e)}")
        
        return {
            "url": url,
            "success": False,
            "description": None,
            "duration": duration,
            "error": str(e)
        }

async def test_with_mock():
    """使用模拟服务进行测试"""
    print("🔧 使用模拟服务进行测试...")
    
    # 模拟 OpenAI 客户端
    async def mock_openai(url):
        # 模拟网络延迟
        await asyncio.sleep(0.5)
        return f"这是一张来自 {url.split('/')[2]} 的商品图片，显示了某种产品的详细信息。"
    
    # 模拟缓存和数据库
    class MockCache:
        def __init__(self):
            self.data = {}
        async def init(self): pass
        async def get(self, key): return self.data.get(key)
        async def set(self, key, value): self.data[key] = value
    
    class MockDB:
        def __init__(self):
            self.data = {}
        async def init(self): pass
        async def fetch_desc(self, url): return self.data.get(url)
        async def upsert_desc(self, url, desc): self.data[url] = desc
    
    # 替换服务组件
    from img_describer import service
    import img_describer.openai_client as openai_client
    
    # 保存原始函数
    original_describe = openai_client.describe_by_openai
    
    # 应用模拟
    openai_client.describe_by_openai = mock_openai
    service.cache = MockCache()
    service.db = MockDB()
    
    await service.cache.init()
    await service.db.init()
    
    results = []
    
    # 测试前5个URL
    test_urls = TEST_URLS[:5]
    
    for i, url in enumerate(test_urls):
        result = await test_single_url(url, i)
        results.append(result)
        
        # 避免请求过快
        if i < len(test_urls) - 1:
            await asyncio.sleep(1)
    
    # 恢复原始函数
    openai_client.describe_by_openai = original_describe
    
    return results

def print_summary(results: List[dict]):
    """打印测试总结"""
    print(f"\n{'='*80}")
    print("📊 测试总结")
    print('='*80)
    
    total = len(results)
    success = sum(1 for r in results if r['success'])
    failed = total - success
    
    total_time = sum(r['duration'] for r in results)
    avg_time = total_time / total if total > 0 else 0
    
    print(f"总测试数: {total}")
    print(f"成功: {success} ✅")
    print(f"失败: {failed} ❌")
    print(f"成功率: {success/total*100:.1f}%")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均耗时: {avg_time:.2f}秒/个")
    
    if failed > 0:
        print(f"\n❌ 失败的URL:")
        for r in results:
            if not r['success']:
                print(f"  - {r['url']}")
                print(f"    错误: {r['error']}")

async def main():
    """主测试函数"""
    print("🚀 开始图片URL描述测试...")
    print(f"📋 准备测试 {len(TEST_URLS)} 个图片URL")
    
    try:
        # 首先检查是否有OpenAI配置
        from img_describer.config import settings
        
        if not settings.openai_credentials or settings.openai_credentials == '[]':
            print("⚠️  未配置OpenAI凭证，使用模拟服务进行测试")
            results = await test_with_mock()
        else:
            print("✅ 检测到OpenAI配置，使用真实服务进行测试")
            print("⚠️  注意：这将消耗OpenAI API配额")
            
            # 初始化服务
            from img_describer.service import init_resources
            await init_resources()
            
            results = []
            # 只测试前3个URL以节省配额
            test_urls = TEST_URLS[:3]
            
            for i, url in enumerate(test_urls):
                result = await test_single_url(url, i)
                results.append(result)
                
                # 避免请求过快
                if i < len(test_urls) - 1:
                    await asyncio.sleep(2)
        
        print_summary(results)
        
        return len([r for r in results if r['success']]) > 0
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
