# Docker Compose 配置 - Consul 服务发现架构
# 包含 Consul、Nginx 网关和微服务

version: '3.9'

networks:
  microservices:
    driver: bridge
    name: microservices

volumes:
  consul_data:
    name: consul_data
  postgres_data:
    name: postgres_data
  redis_data:
    name: redis_data
  nginx_config:
    name: nginx_config

services:
  # Consul 服务发现
  consul:
    image: hashicorp/consul:1.16
    container_name: consul-server
    hostname: consul
    command: >
      consul agent -server -bootstrap-expect=1 -datacenter=dc1 -data-dir=/consul/data
      -retry-join=consul -ui -bind=0.0.0.0 -client=0.0.0.0 -log-level=INFO
    environment:
      - CONSUL_BIND_INTERFACE=eth0
    ports:
      - "8500:8500"  # Consul UI
      - "8600:8600/udp"  # DNS
    volumes:
      - consul_data:/consul/data
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "consul", "members"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped

  # Consul Template - 动态生成 Nginx 配置
  consul-template:
    image: hashicorp/consul-template:0.32
    container_name: consul-template
    hostname: consul-template
    depends_on:
      consul:
        condition: service_healthy
    volumes:
      - ./consul-template:/templates:ro
      - nginx_config:/etc/nginx/conf.d
    networks:
      - microservices
    command: >
      consul-template
      -consul-addr=consul:8500
      -template="/templates/nginx.conf.tpl:/etc/nginx/conf.d/services.conf:nginx -s reload"
      -wait="2s:10s"
      -log-level=info
    restart: unless-stopped

  # Nginx 网关
  nginx:
    image: nginx:1.25-alpine
    container_name: nginx-gateway
    hostname: nginx
    depends_on:
      consul:
        condition: service_healthy
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - nginx_config:/etc/nginx/conf.d
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/nginx-health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: redis-cache
    hostname: redis
    volumes:
      - redis_data:/data
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 5s
    restart: unless-stopped
    # 如果需要密码，取消注释下面的行
    # command: redis-server --requirepass your_redis_password

  # PostgreSQL 数据库
  postgres:
    image: postgres:16-alpine
    container_name: postgres-db
    hostname: postgres
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: mydb
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - microservices
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d mydb"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped

  # 图片描述服务
  say-img-description:
    build: .
    container_name: say-img-description-server
    hostname: say-img-description
    depends_on:
      consul:
        condition: service_healthy
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    environment:
      # Consul 配置
      - CONSUL_HOST=consul
      - CONSUL_PORT=8500
      
      # 服务配置
      - SERVICE_NAME=say-img-description
      - SERVICE_PORT=8000
      - SERVICE_TAGS=api,image,ai
      - ENVIRONMENT=production
      
      # 数据库配置
      - DATABASE_URL=****************************************/mydb
      - REDIS_URL=redis://redis:6379/0
      
      # 应用配置
      - HOST=0.0.0.0
      - LOG_LEVEL=INFO
    env_file:
      - .env
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8000/health"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    # 不暴露端口，只通过网关访问
    command: ["python", "run_consul_api.py"]

  # 可选：Consul UI 的反向代理（如果需要外部访问）
  consul-ui:
    image: nginx:alpine
    container_name: consul-ui-proxy
    depends_on:
      consul:
        condition: service_healthy
    ports:
      - "8501:80"
    networks:
      - microservices
    volumes:
      - ./nginx/consul-ui.conf:/etc/nginx/conf.d/default.conf:ro
    restart: unless-stopped


