#!/usr/bin/env python3
"""测试 Redis 配置功能"""
import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_redis_config():
    """测试 Redis 配置"""
    # 直接导入配置模块，避免导入其他依赖
    import importlib.util
    spec = importlib.util.spec_from_file_location("config", "src/img_describer/config.py")
    config_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(config_module)
    Settings = config_module.Settings
    
    print("=== 测试 Redis 配置 ===")
    
    # 测试1: 使用分别配置的参数
    print("\n1. 测试分别配置参数:")
    os.environ.update({
        'REDIS_HOST': 'localhost',
        'REDIS_PORT': '6379',
        'REDIS_PASSWORD': 'mypassword',
        'REDIS_DB': '5',
        'REDIS_URL': ''  # 清空 URL
    })
    
    settings1 = Settings(_env_file=None)
    redis_url1 = settings1.get_redis_url()
    print(f"  配置: host={settings1.redis_host}, port={settings1.redis_port}, password={settings1.redis_password}, db={settings1.redis_db}")
    print(f"  生成的 URL: {redis_url1}")
    
    # 测试2: 使用完整 URL
    print("\n2. 测试完整 URL:")
    os.environ.update({
        'REDIS_URL': 'redis://:testpass@testhost:6380/3'
    })
    
    settings2 = Settings(_env_file=None)
    redis_url2 = settings2.get_redis_url()
    print(f"  设置的 URL: {settings2.redis_url}")
    print(f"  返回的 URL: {redis_url2}")
    
    # 测试3: 无密码配置
    print("\n3. 测试无密码配置:")
    os.environ.update({
        'REDIS_HOST': 'redis-server',
        'REDIS_PORT': '6379',
        'REDIS_PASSWORD': '',
        'REDIS_DB': '0',
        'REDIS_URL': ''
    })
    
    settings3 = Settings(_env_file=None)
    redis_url3 = settings3.get_redis_url()
    print(f"  配置: host={settings3.redis_host}, port={settings3.redis_port}, password='{settings3.redis_password}', db={settings3.redis_db}")
    print(f"  生成的 URL: {redis_url3}")
    
    print("\n✅ Redis 配置测试完成！")

if __name__ == "__main__":
    test_redis_config()
