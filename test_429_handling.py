#!/usr/bin/env python3
"""测试429错误处理机制"""
import sys
import os
import asyncio
import time
from typing import List, Dict

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def analyze_429_handling_mechanism():
    """分析当前的429错误处理机制"""
    print("🔍 分析当前的429错误处理机制...")
    print("="*80)
    
    print("📋 当前机制分析:")
    print("1. 🎯 错误捕获:")
    print("   - 捕获 openai.RateLimitError (429错误)")
    print("   - 转换为自定义 RateLimitedError 异常")
    
    print("\n2. 🔄 重试策略:")
    print("   - 使用 tenacity 库进行重试")
    print("   - 最大重试次数: 5次")
    print("   - 重试间隔: 固定10秒")
    print("   - 只对 RateLimitedError 进行重试")
    
    print("\n3. 🎲 负载均衡:")
    print("   - 随机选择可用的API凭证")
    print("   - 每次请求都重新随机选择")
    
    print("\n4. ⚠️  当前机制的问题:")
    print("   - 固定10秒等待时间可能不够优化")
    print("   - 没有指数退避策略")
    print("   - 没有根据429响应头调整等待时间")
    print("   - 没有API凭证级别的限流管理")
    
    print("\n5. 🚀 改进建议:")
    print("   - 实现指数退避 (exponential backoff)")
    print("   - 解析429响应头获取建议等待时间")
    print("   - 添加API凭证轮换机制")
    print("   - 实现更智能的限流策略")

async def test_429_scenario():
    """测试429错误场景"""
    print(f"\n{'='*80}")
    print("🧪 测试429错误处理场景")
    print("="*80)
    
    try:
        from img_describer.service import init_resources, describe_image
        
        # 初始化服务
        await init_resources()
        
        # 使用相同的URL快速连续请求，容易触发429
        test_url = "https://basket-25.wbbasket.ru/vol4491/part449105/449105617/images/big/1.webp"
        
        print(f"📋 测试URL: {test_url}")
        print("🚀 发起快速连续请求以测试429处理...")
        
        results = []
        
        # 发起10个快速连续请求
        for i in range(10):
            print(f"\n🔄 请求 {i+1}/10:")
            start_time = time.time()
            
            try:
                description = await describe_image(test_url)
                end_time = time.time()
                duration = end_time - start_time
                
                result = {
                    "request_id": i+1,
                    "success": True,
                    "duration": duration,
                    "description_length": len(description),
                    "error": None
                }
                
                print(f"  ✅ 成功 (耗时: {duration:.2f}秒)")
                print(f"  📝 描述长度: {len(description)} 字符")
                
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                
                result = {
                    "request_id": i+1,
                    "success": False,
                    "duration": duration,
                    "description_length": 0,
                    "error": str(e)
                }
                
                print(f"  ❌ 失败 (耗时: {duration:.2f}秒)")
                print(f"  🚫 错误: {str(e)[:100]}...")
                
                # 如果是429相关错误，分析重试行为
                if "429" in str(e) or "rate" in str(e).lower() or "limit" in str(e).lower():
                    print(f"  🔍 检测到限流相关错误，分析重试行为...")
                    if duration > 40:  # 5次重试 * 10秒 = 50秒左右
                        print(f"  ✅ 重试机制正常工作 (总耗时约{duration:.0f}秒)")
                    else:
                        print(f"  ⚠️  重试时间异常，可能未完全重试")
            
            results.append(result)
            
            # 短暂间隔
            await asyncio.sleep(0.5)
        
        # 分析结果
        print(f"\n{'='*60}")
        print("📊 429处理测试结果分析")
        print("="*60)
        
        successful = [r for r in results if r['success']]
        failed = [r for r in results if not r['success']]
        
        print(f"总请求数: {len(results)}")
        print(f"成功请求: {len(successful)} ✅")
        print(f"失败请求: {len(failed)} ❌")
        
        if successful:
            durations = [r['duration'] for r in successful]
            print(f"\n成功请求耗时分析:")
            print(f"  最快: {min(durations):.2f}秒")
            print(f"  最慢: {max(durations):.2f}秒")
            print(f"  平均: {sum(durations)/len(durations):.2f}秒")
        
        if failed:
            print(f"\n失败请求分析:")
            for r in failed:
                print(f"  请求{r['request_id']}: {r['error'][:80]}...")
        
        return results
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        import traceback
        traceback.print_exc()
        return []

def suggest_improvements():
    """建议改进方案"""
    print(f"\n{'='*80}")
    print("💡 429错误处理改进方案")
    print("="*80)
    
    print("🚀 方案1: 智能指数退避")
    print("   - 第1次重试: 等待2秒")
    print("   - 第2次重试: 等待4秒")
    print("   - 第3次重试: 等待8秒")
    print("   - 第4次重试: 等待16秒")
    print("   - 第5次重试: 等待32秒")
    
    print("\n🎯 方案2: 响应头解析")
    print("   - 解析 Retry-After 头部")
    print("   - 解析 X-RateLimit-Reset 头部")
    print("   - 根据服务器建议调整等待时间")
    
    print("\n🔄 方案3: API凭证轮换")
    print("   - 维护每个API凭证的使用状态")
    print("   - 429错误后暂时禁用该凭证")
    print("   - 自动切换到其他可用凭证")
    
    print("\n⚡ 方案4: 智能限流")
    print("   - 监控每个API的请求频率")
    print("   - 主动限制请求速度")
    print("   - 避免触发429错误")
    
    print("\n🎲 方案5: 请求队列")
    print("   - 实现请求队列机制")
    print("   - 控制并发请求数量")
    print("   - 平滑处理大量请求")

async def main():
    """主函数"""
    print("🚀 开始429错误处理机制测试...")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 分析当前机制
    analyze_429_handling_mechanism()
    
    # 测试429场景
    results = await test_429_scenario()
    
    # 建议改进方案
    suggest_improvements()
    
    print(f"\n🎉 429错误处理机制测试完成!")
    
    return len(results) > 0

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
