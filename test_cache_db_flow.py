#!/usr/bin/env python3
"""测试缓存和数据库存储流程"""
import sys
import os
import asyncio
import time

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def print_step(step, message):
    """打印测试步骤"""
    print(f"\n{'='*60}")
    print(f"🔍 步骤 {step}: {message}")
    print('='*60)

async def clear_test_data(test_url):
    """清除测试数据"""
    print("🧹 清除测试数据...")
    
    try:
        from img_describer.service import cache, db
        
        # 清除 Redis 缓存
        await cache.set(test_url, "")  # 设置为空字符串
        redis_value = await cache.get(test_url)
        print(f"Redis 清除后: {redis_value}")
        
        # 清除 PostgreSQL 数据
        async with db.pool.acquire() as conn:
            await conn.execute("DELETE FROM pj_similar.say_img_description WHERE photo_url = $1", test_url)
            
        print("✅ 测试数据清除完成")
        
    except Exception as e:
        print(f"❌ 清除数据失败: {e}")

async def test_first_request(test_url):
    """测试第一次请求 - 应该调用模型"""
    print_step(1, "第一次请求 - 应该调用模型")
    
    try:
        from img_describer.service import describe_image
        
        print(f"📋 测试URL: {test_url}")
        print("🤖 预期: 调用模型生成描述")
        
        start_time = time.time()
        description = await describe_image(test_url)
        end_time = time.time()
        
        duration = end_time - start_time
        
        print(f"⏱️  耗时: {duration:.2f}秒")
        print(f"📝 描述: {description[:100]}...")
        
        # 第一次请求应该比较慢（调用模型）
        if duration > 2:
            print("✅ 符合预期：耗时较长，说明调用了模型")
        else:
            print("⚠️  耗时较短，可能使用了缓存")
            
        return description
        
    except Exception as e:
        print(f"❌ 第一次请求失败: {e}")
        return None

async def verify_data_storage(test_url, expected_desc):
    """验证数据存储"""
    print_step(2, "验证数据存储")
    
    try:
        from img_describer.service import cache, db
        
        # 检查 Redis 缓存
        redis_desc = await cache.get(test_url)
        redis_ok = redis_desc == expected_desc
        print(f"Redis 存储: {'✅' if redis_ok else '❌'}")
        if redis_desc:
            print(f"  内容: {redis_desc[:50]}...")
        
        # 检查 PostgreSQL 存储
        pg_desc = await db.fetch_desc(test_url)
        pg_ok = pg_desc == expected_desc
        print(f"PostgreSQL 存储: {'✅' if pg_ok else '❌'}")
        if pg_desc:
            print(f"  内容: {pg_desc[:50]}...")
            
        return redis_ok and pg_ok
        
    except Exception as e:
        print(f"❌ 验证存储失败: {e}")
        return False

async def test_second_request_redis_hit(test_url):
    """测试第二次请求 - 应该命中Redis缓存"""
    print_step(3, "第二次请求 - 应该命中Redis缓存")
    
    try:
        from img_describer.service import describe_image
        
        print(f"📋 测试URL: {test_url}")
        print("🚀 预期: 从Redis缓存快速返回")
        
        start_time = time.time()
        description = await describe_image(test_url)
        end_time = time.time()
        
        duration = end_time - start_time
        
        print(f"⏱️  耗时: {duration:.2f}秒")
        print(f"📝 描述: {description[:100]}...")
        
        # 第二次请求应该很快（Redis缓存）
        if duration < 1:
            print("✅ 符合预期：耗时很短，说明使用了Redis缓存")
        else:
            print("⚠️  耗时较长，可能没有使用缓存")
            
        return description
        
    except Exception as e:
        print(f"❌ 第二次请求失败: {e}")
        return None

async def test_redis_miss_pg_hit(test_url):
    """测试Redis缓存失效但PostgreSQL命中的情况"""
    print_step(4, "测试Redis失效，PostgreSQL命中")
    
    try:
        from img_describer.service import cache, describe_image
        
        # 手动清除Redis缓存，但保留PostgreSQL数据
        print("🧹 清除Redis缓存...")
        await cache.set(test_url, "")  # 清空Redis
        
        # 验证Redis已清空
        redis_value = await cache.get(test_url)
        print(f"Redis状态: {'已清空' if not redis_value else '仍有数据'}")
        
        print(f"📋 测试URL: {test_url}")
        print("💾 预期: 从PostgreSQL读取并重新缓存到Redis")
        
        start_time = time.time()
        description = await describe_image(test_url)
        end_time = time.time()
        
        duration = end_time - start_time
        
        print(f"⏱️  耗时: {duration:.2f}秒")
        print(f"📝 描述: {description[:100]}...")
        
        # 应该比模型请求快，但比Redis缓存慢
        if 0.1 < duration < 2:
            print("✅ 符合预期：耗时适中，说明从PostgreSQL读取")
        else:
            print("⚠️  耗时不符合预期")
            
        # 验证Redis是否重新缓存
        redis_value = await cache.get(test_url)
        if redis_value:
            print("✅ Redis已重新缓存数据")
        else:
            print("❌ Redis未重新缓存数据")
            
        return description
        
    except Exception as e:
        print(f"❌ PostgreSQL命中测试失败: {e}")
        return None

async def test_final_redis_hit(test_url):
    """最终测试Redis缓存命中"""
    print_step(5, "最终验证Redis缓存")
    
    try:
        from img_describer.service import describe_image
        
        print(f"📋 测试URL: {test_url}")
        print("🚀 预期: 再次从Redis缓存快速返回")
        
        start_time = time.time()
        description = await describe_image(test_url)
        end_time = time.time()
        
        duration = end_time - start_time
        
        print(f"⏱️  耗时: {duration:.2f}秒")
        print(f"📝 描述: {description[:100]}...")
        
        if duration < 0.5:
            print("✅ 符合预期：Redis缓存工作正常")
        else:
            print("⚠️  Redis缓存可能有问题")
            
        return description
        
    except Exception as e:
        print(f"❌ 最终测试失败: {e}")
        return None

async def main():
    """主测试函数"""
    print("🚀 开始缓存和数据库存储流程测试...")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 使用一个测试URL
    test_url = "https://basket-25.wbbasket.ru/vol4491/part449105/449105617/images/big/1.webp"
    
    try:
        # 初始化服务
        from img_describer.service import init_resources
        await init_resources()
        
        # 清除测试数据
        await clear_test_data(test_url)
        
        # 测试流程
        desc1 = await test_first_request(test_url)  # 调用模型
        if not desc1:
            print("❌ 第一次请求失败，终止测试")
            return False
            
        storage_ok = await verify_data_storage(test_url, desc1)  # 验证存储
        if not storage_ok:
            print("❌ 数据存储验证失败")
            
        desc2 = await test_second_request_redis_hit(test_url)  # Redis命中
        if not desc2:
            print("❌ 第二次请求失败")
            
        desc3 = await test_redis_miss_pg_hit(test_url)  # PostgreSQL命中
        if not desc3:
            print("❌ PostgreSQL命中测试失败")
            
        desc4 = await test_final_redis_hit(test_url)  # 最终Redis命中
        if not desc4:
            print("❌ 最终Redis测试失败")
        
        # 验证所有描述一致
        descriptions = [desc1, desc2, desc3, desc4]
        all_same = all(desc == desc1 for desc in descriptions if desc)
        
        print(f"\n{'='*60}")
        print("📊 测试总结")
        print('='*60)
        
        print(f"✅ 模型调用: {'成功' if desc1 else '失败'}")
        print(f"✅ 数据存储: {'成功' if storage_ok else '失败'}")
        print(f"✅ Redis缓存: {'成功' if desc2 else '失败'}")
        print(f"✅ PostgreSQL读取: {'成功' if desc3 else '失败'}")
        print(f"✅ 数据一致性: {'一致' if all_same else '不一致'}")
        
        if all([desc1, storage_ok, desc2, desc3, desc4, all_same]):
            print("\n🎉 所有缓存和数据库存储测试通过！")
            print("\n✨ 验证的功能:")
            print("  - ✅ 首次请求调用模型生成描述")
            print("  - ✅ 数据同时存储到Redis和PostgreSQL")
            print("  - ✅ 后续请求优先使用Redis缓存")
            print("  - ✅ Redis失效时从PostgreSQL读取")
            print("  - ✅ 从PostgreSQL读取后重新缓存到Redis")
            print("  - ✅ 三级存储策略工作正常")
            return True
        else:
            print("\n❌ 部分测试失败，请检查缓存和数据库配置")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
