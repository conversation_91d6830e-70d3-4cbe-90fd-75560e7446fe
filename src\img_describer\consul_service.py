"""
Consul 服务注册和发现管理组件
"""
import os
import socket
import asyncio
import aiohttp
import json
from typing import Optional, Dict, Any, List
from .logging import logger, GREEN, RED


class ConsulService:
    """Consul 服务管理类"""
    
    def __init__(self):
        self.consul_host = os.getenv("CONSUL_HOST", "localhost")
        self.consul_port = int(os.getenv("CONSUL_PORT", "8500"))
        self.consul_url = f"http://{self.consul_host}:{self.consul_port}"
        
        # 服务配置
        self.service_name = os.getenv("SERVICE_NAME", "img-describer")
        self.service_port = int(os.getenv("SERVICE_PORT", "8000"))
        self.service_address = self._get_service_address()
        self.service_id = f"{self.service_name}-{self.service_address}-{self.service_port}"
        self.service_tags = self._parse_service_tags()
        
        # 健康检查配置
        self.health_check_interval = "10s"
        self.health_check_timeout = "5s"
        self.health_check_deregister_critical_after = "30s"
        
        logger.info(f"[ConsulService] 初始化完成 - 服务: {self.service_name}, 地址: {self.service_address}:{self.service_port}")
    
    def _get_service_address(self) -> str:
        """获取服务地址"""
        # 在容器环境中，使用容器名作为地址
        container_name = os.getenv("HOSTNAME")
        if container_name:
            return container_name
        
        # 本地开发环境，获取本机IP
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                return s.getsockname()[0]
        except Exception:
            return "localhost"
    
    def _parse_service_tags(self) -> List[str]:
        """解析服务标签"""
        tags_str = os.getenv("SERVICE_TAGS", "api")
        return [tag.strip() for tag in tags_str.split(",") if tag.strip()]
    
    async def register(self) -> bool:
        """注册服务到 Consul"""
        service_definition = {
            "ID": self.service_id,
            "Name": self.service_name,
            "Tags": self.service_tags,
            "Address": self.service_address,
            "Port": self.service_port,
            "Check": {
                "HTTP": f"http://{self.service_address}:{self.service_port}/health",
                "Interval": self.health_check_interval,
                "Timeout": self.health_check_timeout,
                "DeregisterCriticalServiceAfter": self.health_check_deregister_critical_after
            },
            "Meta": {
                "version": "1.0.0",
                "environment": os.getenv("ENVIRONMENT", "development")
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.consul_url}/v1/agent/service/register"
                async with session.put(url, json=service_definition) as response:
                    if response.status == 200:
                        logger.info(GREEN + f"✓ 服务注册成功: {self.service_id}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(RED + f"✗ 服务注册失败: {response.status} - {error_text}")
                        return False
        except Exception as e:
            logger.error(RED + f"✗ 服务注册异常: {e}")
            return False
    
    async def deregister(self) -> bool:
        """从 Consul 注销服务"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.consul_url}/v1/agent/service/deregister/{self.service_id}"
                async with session.put(url) as response:
                    if response.status == 200:
                        logger.info(GREEN + f"✓ 服务注销成功: {self.service_id}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(RED + f"✗ 服务注销失败: {response.status} - {error_text}")
                        return False
        except Exception as e:
            logger.error(RED + f"✗ 服务注销异常: {e}")
            return False
    
    async def get_healthy_services(self, service_name: str) -> List[Dict[str, Any]]:
        """获取健康的服务实例"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.consul_url}/v1/health/service/{service_name}?passing=true"
                async with session.get(url) as response:
                    if response.status == 200:
                        services = await response.json()
                        return [
                            {
                                "id": service["Service"]["ID"],
                                "address": service["Service"]["Address"],
                                "port": service["Service"]["Port"],
                                "tags": service["Service"]["Tags"],
                                "meta": service["Service"]["Meta"]
                            }
                            for service in services
                        ]
                    else:
                        logger.error(RED + f"✗ 获取服务实例失败: {response.status}")
                        return []
        except Exception as e:
            logger.error(RED + f"✗ 获取服务实例异常: {e}")
            return []
    
    async def discover_service(self, service_name: str) -> Optional[Dict[str, Any]]:
        """发现服务实例（负载均衡选择一个）"""
        services = await self.get_healthy_services(service_name)
        if not services:
            logger.warning(f"未找到健康的服务实例: {service_name}")
            return None
        
        # 简单的轮询负载均衡（这里可以实现更复杂的策略）
        import random
        selected_service = random.choice(services)
        logger.info(f"发现服务实例: {service_name} -> {selected_service['address']}:{selected_service['port']}")
        return selected_service
    
    async def health_check(self) -> Dict[str, Any]:
        """执行健康检查"""
        try:
            # 检查 Consul 连接
            consul_healthy = await self._check_consul_connection()
            
            # 这里可以添加更多的健康检查逻辑
            # 比如检查数据库连接、缓存连接等
            
            return {
                "status": "healthy" if consul_healthy else "unhealthy",
                "service": self.service_name,
                "service_id": self.service_id,
                "version": "1.0.0",
                "checks": {
                    "consul": "ok" if consul_healthy else "error"
                }
            }
        except Exception as e:
            logger.error(RED + f"✗ 健康检查异常: {e}")
            return {
                "status": "unhealthy",
                "service": self.service_name,
                "error": str(e)
            }
    
    async def _check_consul_connection(self) -> bool:
        """检查 Consul 连接状态"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.consul_url}/v1/status/leader"
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=3)) as response:
                    return response.status == 200
        except Exception:
            return False
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "service_name": self.service_name,
            "service_id": self.service_id,
            "address": self.service_address,
            "port": self.service_port,
            "tags": self.service_tags,
            "consul_url": self.consul_url
        }


# 全局实例
consul_service = ConsulService()
