"""改进版的OpenAI客户端，包含更智能的429错误处理"""
import json
import random
import asyncio
import time
from typing import Dict, List, Optional
import openai
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from .config import settings
from .logging import logger, RED, GRE<PERSON>

try:
    _CREDENTIALS = json.loads(settings.openai_credentials)
    if not _CREDENTIALS:
        logger.warning(RED + "未检测到 OPENAI_CREDENTIALS，程序可能无法调用 OpenAI 接口！")
except json.JSONDecodeError:
    logger.error(RED + "OPENAI_CREDENTIALS 格式错误，应为 JSON 数组")
    _CREDENTIALS = []


class RateLimitedError(Exception):
    """包装 429 错误"""
    def __init__(self, message: str, retry_after: Optional[int] = None):
        super().__init__(message)
        self.retry_after = retry_after


class APICredentialManager:
    """API凭证管理器"""
    def __init__(self, credentials: List[Dict]):
        self.credentials = credentials
        self.blocked_until = {}  # 记录每个凭证被阻塞到什么时候
        self.usage_count = {}    # 记录每个凭证的使用次数
        
    def get_available_credential(self) -> Optional[Dict]:
        """获取可用的API凭证"""
        current_time = time.time()
        available_creds = []
        
        for i, cred in enumerate(self.credentials):
            # 检查是否还在阻塞期
            if i in self.blocked_until and current_time < self.blocked_until[i]:
                continue
            available_creds.append((i, cred))
        
        if not available_creds:
            return None
            
        # 优先选择使用次数较少的凭证
        available_creds.sort(key=lambda x: self.usage_count.get(x[0], 0))
        cred_index, cred = available_creds[0]
        
        # 更新使用计数
        self.usage_count[cred_index] = self.usage_count.get(cred_index, 0) + 1
        
        return cred
    
    def block_credential(self, cred: Dict, duration: int = 60):
        """阻塞某个凭证一段时间"""
        for i, c in enumerate(self.credentials):
            if c == cred:
                self.blocked_until[i] = time.time() + duration
                logger.warning(RED + f"API凭证 {i+1} 被阻塞 {duration} 秒")
                break


# 全局凭证管理器
credential_manager = APICredentialManager(_CREDENTIALS)


def parse_retry_after(response_headers: Dict) -> int:
    """解析响应头中的重试时间"""
    # 尝试解析 Retry-After 头
    if 'retry-after' in response_headers:
        try:
            return int(response_headers['retry-after'])
        except ValueError:
            pass
    
    # 尝试解析 X-RateLimit-Reset 头
    if 'x-ratelimit-reset' in response_headers:
        try:
            reset_time = int(response_headers['x-ratelimit-reset'])
            current_time = int(time.time())
            return max(1, reset_time - current_time)
        except ValueError:
            pass
    
    # 默认返回30秒
    return 30


@retry(
    stop=stop_after_attempt(5), 
    wait=wait_exponential(multiplier=2, min=2, max=60),  # 指数退避: 2, 4, 8, 16, 32秒
    retry=retry_if_exception_type(RateLimitedError), 
    reraise=True
)
async def describe_by_openai_improved(photo_url: str) -> str:
    """改进版的OpenAI图片描述函数"""
    if not _CREDENTIALS:
        raise ValueError("没有可用的 OpenAI 凭证")

    # 获取可用凭证
    cred = credential_manager.get_available_credential()
    if not cred:
        # 所有凭证都被阻塞，等待最短的阻塞时间
        min_wait = min(credential_manager.blocked_until.values()) - time.time()
        if min_wait > 0:
            logger.warning(RED + f"所有API凭证都被阻塞，等待 {min_wait:.0f} 秒...")
            await asyncio.sleep(min_wait)
            cred = credential_manager.get_available_credential()
        
        if not cred:
            raise ValueError("没有可用的 OpenAI 凭证")

    client = openai.AsyncOpenAI(
        api_key=cred["api_key"], 
        base_url=cred["base_url"], 
        timeout=settings.openai_timeout
    )
    
    try:
        res = await client.chat.completions.create(
            model=cred["model"],
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "image_url", "image_url": {"url": photo_url, "detail": "low"}},
                        {"type": "text", "text": "请用中文描述这张图片的主要内容。"},
                    ],
                }
            ],
        )
        
        # 请求成功，记录日志
        logger.info(GREEN + f"OpenAI 生成成功: {photo_url}")
        return res.choices[0].message.content.strip()
        
    except openai.RateLimitError as e:
        # 解析重试时间
        retry_after = 30  # 默认30秒
        if hasattr(e, 'response') and e.response:
            retry_after = parse_retry_after(e.response.headers)
        
        # 阻塞当前凭证
        credential_manager.block_credential(cred, retry_after)
        
        logger.warning(RED + f"遇到429错误，建议等待 {retry_after} 秒")
        raise RateLimitedError(f"Rate limit exceeded, retry after {retry_after}s", retry_after)
        
    except Exception as e:
        logger.error(RED + f"OpenAI 请求失败: {e}")
        raise


# 请求队列管理
class RequestQueue:
    """请求队列，用于控制并发数量"""
    def __init__(self, max_concurrent: int = 10):
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.queue_size = 0
        
    async def process_request(self, photo_url: str) -> str:
        """通过队列处理请求"""
        async with self.semaphore:
            self.queue_size += 1
            try:
                result = await describe_by_openai_improved(photo_url)
                return result
            finally:
                self.queue_size -= 1


# 全局请求队列
request_queue = RequestQueue(max_concurrent=20)  # 最大20个并发


async def describe_by_openai_with_queue(photo_url: str) -> str:
    """通过队列处理的OpenAI请求"""
    return await request_queue.process_request(photo_url)
