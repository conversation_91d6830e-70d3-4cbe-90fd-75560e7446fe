# Redis 配置 - 两种方式任选其一
# 方式1: 分别配置各项参数
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=5

# 方式2: 使用完整的 Redis URL（如果设置了 REDIS_URL，会优先使用）
# 无密码: redis://redis:6379/5
# 有密码: redis://:your_password@redis:6379/5
# 外部Redis: redis://:password@your-redis-host:6379/5
REDIS_URL=

# PostgreSQL
PG_USER=user
PG_PASSWORD=password
PG_DB=mydb
PG_HOST=postgres
PG_PORT=5432

# OpenAI (JSON 字符串)
OPENAI_CREDENTIALS=[{"api_key":"sk-...","base_url":"https://api.openai.com/v1","model":"gpt-4o-mini"}]
