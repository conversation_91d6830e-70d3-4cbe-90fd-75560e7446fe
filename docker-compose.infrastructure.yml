# Docker Compose 配置 - 基础设施服务（主服务）
# 包含 Consul、Nginx 网关、<PERSON>is、PostgreSQL
# 用于独立部署基础设施，业务服务可以单独部署并连接到此基础设施

version: '3.9'

networks:
  microservices:
    driver: bridge
    name: microservices
    external: false

volumes:
  consul_data:
    name: consul_data
  postgres_data:
    name: postgres_data
  redis_data:
    name: redis_data
  nginx_config:
    name: nginx_config

services:
  # Consul 服务发现
  consul:
    image: hashicorp/consul:1.16
    container_name: consul-server
    hostname: consul
    command: >
      consul agent -server -bootstrap-expect=1 -datacenter=dc1 -data-dir=/consul/data
      -retry-join=consul -ui -bind=0.0.0.0 -client=0.0.0.0 -log-level=INFO
    environment:
      - CONSUL_BIND_INTERFACE=eth0
    ports:
      - "8500:8500"  # Consul UI
      - "8600:8600/udp"  # DNS
    volumes:
      - consul_data:/consul/data
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "consul", "members"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped

  # Consul Template - 动态生成 Nginx 配置
  consul-template:
    image: hashicorp/consul-template:0.32
    container_name: consul-template
    hostname: consul-template
    user: "0:0"  # 使用root用户解决权限问题
    depends_on:
      consul:
        condition: service_healthy
    volumes:
      - ./consul-template:/templates:ro
      - nginx_config:/etc/nginx/conf.d
    networks:
      - microservices
    command: >
      consul-template
      -consul-addr=consul:8500
      -template="/templates/nginx.conf.tpl:/etc/nginx/conf.d/services.conf"
      -wait="2s:10s"
      -log-level=info
    restart: unless-stopped

  # Nginx 网关
  nginx:
    image: nginx:1.25-alpine
    container_name: nginx-gateway
    hostname: nginx
    depends_on:
      consul:
        condition: service_healthy
    ports:
      - "80:80"
      - "443:443"  # 为 HTTPS 预留
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - nginx_config:/etc/nginx/conf.d
      # 如果需要 SSL 证书，取消注释下面的行
      # - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/nginx-health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    restart: unless-stopped

  # 注意：由于您使用外部的Redis和PostgreSQL，这里注释掉本地数据库服务
  # 如果需要使用本地数据库，可以取消注释

  # Redis 缓存 (使用外部Redis: ************:6379)
  # redis:
  #   image: redis:7-alpine
  #   container_name: redis-cache
  #   hostname: redis
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - microservices
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 3
  #     start_period: 5s
  #   restart: unless-stopped

  # PostgreSQL 数据库 (使用外部PostgreSQL: ************:5432)
  # postgres:
  #   image: postgres:16-alpine
  #   container_name: postgres-db
  #   hostname: postgres
  #   environment:
  #     POSTGRES_USER: user
  #     POSTGRES_PASSWORD: password
  #     POSTGRES_DB: mydb
  #     POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - microservices
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U user -d mydb"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5
  #     start_period: 10s
  #   restart: unless-stopped

  # 可选：Consul UI 的反向代理（如果需要外部访问）
  consul-ui:
    image: nginx:alpine
    container_name: consul-ui-proxy
    depends_on:
      consul:
        condition: service_healthy
    ports:
      - "8501:80"
    networks:
      - microservices
    volumes:
      - ./nginx/consul-ui.conf:/etc/nginx/conf.d/default.conf:ro
    restart: unless-stopped

  # 可选：监控服务（Prometheus + Grafana）
  # prometheus:
  #   image: prom/prometheus:latest
  #   container_name: prometheus
  #   hostname: prometheus
  #   ports:
  #     - "9090:9090"
  #   volumes:
  #     - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
  #   networks:
  #     - microservices
  #   restart: unless-stopped

  # grafana:
  #   image: grafana/grafana:latest
  #   container_name: grafana
  #   hostname: grafana
  #   ports:
  #     - "3000:3000"
  #   environment:
  #     - GF_SECURITY_ADMIN_PASSWORD=admin
  #   volumes:
  #     - grafana_data:/var/lib/grafana
  #   networks:
  #     - microservices
  #   restart: unless-stopped

# 可选：监控数据卷
# volumes:
#   grafana_data:
#     name: grafana_data
