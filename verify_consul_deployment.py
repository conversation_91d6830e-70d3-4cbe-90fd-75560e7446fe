#!/usr/bin/env python3
"""
验证 Consul 微服务架构部署的脚本
"""
import asyncio
import aiohttp
import json
import time
from typing import Dict, List, Optional


class ConsulDeploymentVerifier:
    """Consul 部署验证器"""
    
    def __init__(self):
        self.gateway_url = "http://localhost"
        self.consul_url = "http://localhost:8500"
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10))
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def check_service(self, url: str, name: str) -> bool:
        """检查服务是否可访问"""
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    print(f"✓ {name} 服务正常 ({response.status})")
                    return True
                else:
                    print(f"✗ {name} 服务异常 ({response.status})")
                    return False
        except Exception as e:
            print(f"✗ {name} 服务连接失败: {e}")
            return False
    
    async def check_consul_services(self) -> Dict[str, List]:
        """检查 Consul 中注册的服务"""
        try:
            url = f"{self.consul_url}/v1/agent/services"
            async with self.session.get(url) as response:
                if response.status == 200:
                    services = await response.json()
                    print(f"✓ Consul 服务发现正常，注册服务数: {len(services)}")
                    
                    for service_id, service_info in services.items():
                        print(f"  - {service_info['Service']}: {service_info['Address']}:{service_info['Port']}")
                        print(f"    标签: {', '.join(service_info['Tags'])}")
                    
                    return services
                else:
                    print(f"✗ Consul 服务发现异常 ({response.status})")
                    return {}
        except Exception as e:
            print(f"✗ Consul 连接失败: {e}")
            return {}
    
    async def check_service_health(self, service_name: str) -> bool:
        """检查服务健康状态"""
        try:
            url = f"{self.gateway_url}/api/{service_name}/health"
            async with self.session.get(url) as response:
                if response.status == 200:
                    health_data = await response.json()
                    status = health_data.get("status", "unknown")
                    print(f"✓ {service_name} 健康检查: {status}")
                    return status == "healthy"
                else:
                    print(f"✗ {service_name} 健康检查失败 ({response.status})")
                    return False
        except Exception as e:
            print(f"✗ {service_name} 健康检查异常: {e}")
            return False
    
    async def test_service_api(self, service_name: str, endpoint: str, params: Dict = None) -> bool:
        """测试服务 API"""
        try:
            url = f"{self.gateway_url}/api/{service_name}{endpoint}"
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✓ {service_name} API 测试成功")
                    print(f"  响应: {json.dumps(data, ensure_ascii=False, indent=2)[:200]}...")
                    return True
                else:
                    error_text = await response.text()
                    print(f"✗ {service_name} API 测试失败 ({response.status}): {error_text[:100]}...")
                    return False
        except Exception as e:
            print(f"✗ {service_name} API 测试异常: {e}")
            return False
    
    async def check_load_balancing(self, service_name: str, requests_count: int = 5) -> bool:
        """检查负载均衡"""
        print(f"测试 {service_name} 负载均衡 ({requests_count} 次请求)...")
        
        servers = set()
        success_count = 0
        
        for i in range(requests_count):
            try:
                url = f"{self.gateway_url}/api/{service_name}/info"
                async with self.session.get(url) as response:
                    if response.status == 200:
                        # 检查响应头中的服务器信息
                        server_info = response.headers.get('X-Served-By', 'unknown')
                        servers.add(server_info)
                        success_count += 1
                        print(f"  请求 {i+1}: ✓ (服务器: {server_info})")
                    else:
                        print(f"  请求 {i+1}: ✗ ({response.status})")
            except Exception as e:
                print(f"  请求 {i+1}: ✗ ({e})")
            
            await asyncio.sleep(0.1)  # 短暂延迟
        
        print(f"负载均衡测试结果: {success_count}/{requests_count} 成功")
        print(f"发现服务器: {servers}")
        
        return success_count > 0
    
    async def run_full_verification(self):
        """运行完整验证"""
        print("=" * 60)
        print("Consul 微服务架构部署验证")
        print("=" * 60)
        
        results = {}
        
        # 1. 检查基础服务
        print("\n1. 检查基础服务...")
        results['gateway'] = await self.check_service(f"{self.gateway_url}/gateway/status", "API 网关")
        results['consul'] = await self.check_service(f"{self.consul_url}/v1/status/leader", "Consul")
        
        # 2. 检查服务发现
        print("\n2. 检查服务发现...")
        consul_services = await self.check_consul_services()
        results['service_discovery'] = len(consul_services) > 0
        
        # 3. 检查服务健康状态
        print("\n3. 检查服务健康状态...")
        api_services = [name for name, info in consul_services.items() 
                       if 'api' in info.get('Tags', [])]
        
        health_results = {}
        for service_id in api_services:
            service_name = consul_services[service_id]['Service']
            health_results[service_name] = await self.check_service_health(service_name)
        
        results['health_checks'] = all(health_results.values())
        
        # 4. 测试 API 功能
        print("\n4. 测试 API 功能...")
        api_results = {}
        
        # 测试图片描述服务
        if 'img-describer' in [consul_services[sid]['Service'] for sid in api_services]:
            api_results['img-describer'] = await self.test_service_api(
                'img-describer', 
                '/describe',
                {'url': 'https://via.placeholder.com/300x200.png?text=Test+Image'}
            )
        
        results['api_tests'] = all(api_results.values()) if api_results else False
        
        # 5. 测试负载均衡
        print("\n5. 测试负载均衡...")
        lb_results = {}
        for service_id in api_services:
            service_name = consul_services[service_id]['Service']
            lb_results[service_name] = await self.check_load_balancing(service_name)
        
        results['load_balancing'] = any(lb_results.values()) if lb_results else False
        
        # 6. 生成报告
        print("\n" + "=" * 60)
        print("验证结果汇总")
        print("=" * 60)
        
        total_checks = len(results)
        passed_checks = sum(1 for result in results.values() if result)
        
        for check_name, result in results.items():
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{check_name:20} : {status}")
        
        print(f"\n总体结果: {passed_checks}/{total_checks} 项检查通过")
        
        if passed_checks == total_checks:
            print("🎉 所有检查通过！Consul 微服务架构部署成功！")
            return True
        else:
            print("⚠️  部分检查失败，请检查服务状态和配置。")
            return False


async def main():
    """主函数"""
    print("开始验证 Consul 微服务架构部署...")
    print("请确保已启动服务: docker-compose -f docker-compose.consul.yml up -d")
    print()
    
    # 等待服务启动
    print("等待服务启动...")
    await asyncio.sleep(5)
    
    async with ConsulDeploymentVerifier() as verifier:
        success = await verifier.run_full_verification()
        
        if success:
            print("\n🚀 部署验证完成！可以开始使用微服务了。")
            print("\n常用端点:")
            print("- API 网关: http://localhost")
            print("- Consul UI: http://localhost:8500")
            print("- 服务列表: http://localhost/api/services")
            print("- 图片描述: http://localhost/api/img-describer/describe?url=IMAGE_URL")
        else:
            print("\n❌ 部署验证失败，请检查服务状态。")
            print("\n故障排除:")
            print("1. 检查容器状态: docker-compose -f docker-compose.consul.yml ps")
            print("2. 查看日志: docker-compose -f docker-compose.consul.yml logs")
            print("3. 重启服务: docker-compose -f docker-compose.consul.yml restart")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n验证被用户中断")
    except Exception as e:
        print(f"\n验证过程异常: {e}")
