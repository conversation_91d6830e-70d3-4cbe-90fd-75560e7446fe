from fastapi import FastAPI, HTTPException, Query
from .service import describe_image, init_resources
from .logging import logger, RED, GREEN
from .consul_service import consul_service
from .service_client import service_client, cleanup_service_client

app = FastAPI(title="图片描述服务（Consul 模式）")


@app.on_event("startup")
async def on_startup():
    """应用启动时的初始化"""
    try:
        # 初始化业务资源
        await init_resources()
        logger.info(GREEN + "✓ 业务资源初始化完成")

        # 注册到 Consul
        success = await consul_service.register()
        if success:
            logger.info(GREEN + "✓ Consul 服务注册成功")
        else:
            logger.error(RED + "✗ Consul 服务注册失败")

    except Exception as e:
        logger.error(RED + f"✗ 应用启动失败: {e}")
        raise


@app.on_event("shutdown")
async def on_shutdown():
    """应用关闭时的清理"""
    try:
        # 从 Consul 注销服务
        await consul_service.deregister()
        logger.info(GREEN + "✓ Consul 服务注销完成")

        # 清理服务客户端
        await cleanup_service_client()
        logger.info(GREEN + "✓ 服务客户端清理完成")

    except Exception as e:
        logger.error(RED + f"✗ 应用关闭清理失败: {e}")


@app.get("/health")
async def health_check():
    """健康检查端点 - Consul 要求"""
    try:
        health_status = await consul_service.health_check()
        return health_status
    except Exception as e:
        logger.error(RED + f"✗ 健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.get("/info")
async def service_info():
    """服务信息端点"""
    try:
        return consul_service.get_service_info()
    except Exception as e:
        logger.error(RED + f"✗ 获取服务信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/describe")
async def describe_endpoint(url: str = Query(..., description="图片 URL")):
    """图片描述接口"""
    try:
        desc = await describe_image(url)
        return {"url": url, "description": desc}
    except Exception as e:
        logger.error(RED + f"✗ 图片描述处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
