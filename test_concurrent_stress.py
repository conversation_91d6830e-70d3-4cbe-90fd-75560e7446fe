#!/usr/bin/env python3
"""高并发压力测试"""
import sys
import os
import asyncio
import time
import random
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor
import statistics

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 导入图片URL生成函数
sys.path.insert(0, os.path.dirname(__file__))
from test_creat_img_url import parse_product_url, nm_ids

def generate_test_urls(count: int) -> List[str]:
    """生成测试用的图片URL"""
    selected_ids = random.sample(nm_ids, min(count, len(nm_ids)))
    urls = []
    for nm_id in selected_ids:
        img_url = parse_product_url(nm_id, 1)['parse_product_urls'][0]
        urls.append(img_url)
    return urls

async def single_request(url: str, request_id: int) -> Dict:
    """单个请求测试"""
    start_time = time.time()
    
    try:
        from img_describer.service import describe_image
        
        description = await describe_image(url)
        
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            "request_id": request_id,
            "url": url,
            "success": True,
            "duration": duration,
            "description_length": len(description),
            "error": None
        }
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            "request_id": request_id,
            "url": url,
            "success": False,
            "duration": duration,
            "description_length": 0,
            "error": str(e)
        }

async def concurrent_batch_test(urls: List[str], batch_size: int = 50) -> List[Dict]:
    """并发批次测试"""
    print(f"🚀 开始并发测试: {len(urls)} 个URL, 批次大小: {batch_size}")
    
    results = []
    
    # 分批处理
    for i in range(0, len(urls), batch_size):
        batch_urls = urls[i:i + batch_size]
        batch_num = i // batch_size + 1
        
        print(f"\n📦 批次 {batch_num}: 处理 {len(batch_urls)} 个请求...")
        
        # 创建并发任务
        tasks = []
        for j, url in enumerate(batch_urls):
            request_id = i + j + 1
            task = single_request(url, request_id)
            tasks.append(task)
        
        # 执行并发请求
        batch_start = time.time()
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        batch_end = time.time()
        
        batch_duration = batch_end - batch_start
        
        # 处理结果
        valid_results = []
        for result in batch_results:
            if isinstance(result, dict):
                valid_results.append(result)
            else:
                # 处理异常
                valid_results.append({
                    "request_id": len(valid_results) + 1,
                    "url": "unknown",
                    "success": False,
                    "duration": 0,
                    "description_length": 0,
                    "error": str(result)
                })
        
        results.extend(valid_results)
        
        # 批次统计
        success_count = sum(1 for r in valid_results if r['success'])
        avg_duration = statistics.mean([r['duration'] for r in valid_results])
        
        print(f"  ✅ 成功: {success_count}/{len(batch_urls)}")
        print(f"  ⏱️  批次总耗时: {batch_duration:.2f}秒")
        print(f"  📊 平均单请求耗时: {avg_duration:.2f}秒")
        print(f"  🚀 并发效率: {len(batch_urls)/batch_duration:.2f} 请求/秒")
        
        # 批次间隔，避免过度压力
        if i + batch_size < len(urls):
            print("  ⏳ 等待 2 秒后继续下一批次...")
            await asyncio.sleep(2)
    
    return results

def analyze_results(results: List[Dict]):
    """分析测试结果"""
    print(f"\n{'='*80}")
    print("📊 压力测试结果分析")
    print('='*80)
    
    total_requests = len(results)
    successful_requests = [r for r in results if r['success']]
    failed_requests = [r for r in results if not r['success']]
    
    success_count = len(successful_requests)
    failure_count = len(failed_requests)
    success_rate = (success_count / total_requests) * 100 if total_requests > 0 else 0
    
    print(f"📈 总体统计:")
    print(f"  总请求数: {total_requests}")
    print(f"  成功请求: {success_count} ✅")
    print(f"  失败请求: {failure_count} ❌")
    print(f"  成功率: {success_rate:.1f}%")
    
    if successful_requests:
        durations = [r['duration'] for r in successful_requests]
        desc_lengths = [r['description_length'] for r in successful_requests]
        
        print(f"\n⏱️  响应时间分析:")
        print(f"  最快响应: {min(durations):.2f}秒")
        print(f"  最慢响应: {max(durations):.2f}秒")
        print(f"  平均响应: {statistics.mean(durations):.2f}秒")
        print(f"  中位数响应: {statistics.median(durations):.2f}秒")
        
        # 响应时间分布
        fast_requests = [d for d in durations if d < 1]  # 缓存命中
        medium_requests = [d for d in durations if 1 <= d < 5]  # 数据库命中
        slow_requests = [d for d in durations if d >= 5]  # 模型请求
        
        print(f"\n🚀 响应时间分布:")
        print(f"  快速响应 (<1秒): {len(fast_requests)} ({len(fast_requests)/success_count*100:.1f}%) - 缓存命中")
        print(f"  中等响应 (1-5秒): {len(medium_requests)} ({len(medium_requests)/success_count*100:.1f}%) - 数据库命中")
        print(f"  慢速响应 (≥5秒): {len(slow_requests)} ({len(slow_requests)/success_count*100:.1f}%) - 模型请求")
        
        print(f"\n📝 描述长度分析:")
        print(f"  平均长度: {statistics.mean(desc_lengths):.0f} 字符")
        print(f"  最短描述: {min(desc_lengths)} 字符")
        print(f"  最长描述: {max(desc_lengths)} 字符")
    
    if failed_requests:
        print(f"\n❌ 失败请求分析:")
        error_types = {}
        for r in failed_requests:
            error = r['error'][:50] + "..." if len(r['error']) > 50 else r['error']
            error_types[error] = error_types.get(error, 0) + 1
        
        for error, count in error_types.items():
            print(f"  {error}: {count} 次")

async def performance_benchmark():
    """性能基准测试"""
    print(f"\n{'='*80}")
    print("🎯 性能基准测试")
    print('='*80)
    
    # 测试不同并发级别
    concurrency_levels = [1, 5, 10, 20, 50]
    test_url = generate_test_urls(1)[0]  # 使用同一个URL测试缓存性能
    
    for concurrency in concurrency_levels:
        print(f"\n🔄 测试并发级别: {concurrency}")
        
        # 预热缓存
        if concurrency == 1:
            from img_describer.service import describe_image
            await describe_image(test_url)
            print("  🔥 缓存预热完成")
        
        # 并发测试
        tasks = [single_request(test_url, i) for i in range(concurrency)]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        total_time = end_time - start_time
        successful = [r for r in results if r['success']]
        
        print(f"  ⏱️  总耗时: {total_time:.2f}秒")
        print(f"  🚀 吞吐量: {len(successful)/total_time:.2f} 请求/秒")
        print(f"  📊 平均响应时间: {statistics.mean([r['duration'] for r in successful]):.3f}秒")

async def main():
    """主测试函数"""
    print("🚀 开始高并发压力测试...")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 初始化服务
        from img_describer.service import init_resources
        await init_resources()
        print("✅ 服务初始化完成")
        
        # 生成测试URL
        test_count = 100  # 测试100个不同的图片
        test_urls = generate_test_urls(test_count)
        print(f"📋 生成 {len(test_urls)} 个测试URL")
        
        # 执行并发测试
        results = await concurrent_batch_test(test_urls, batch_size=50)
        
        # 分析结果
        analyze_results(results)
        
        # 性能基准测试
        await performance_benchmark()
        
        print(f"\n🎉 压力测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
