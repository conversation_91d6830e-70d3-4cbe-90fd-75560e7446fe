#!/usr/bin/env python3
"""全并发极限压力测试 - 处理所有434个图片URL"""
import sys
import os
import asyncio
import time
import statistics
from typing import List, Dict
import psutil
import gc

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 导入图片URL生成函数
sys.path.insert(0, os.path.dirname(__file__))
from test_creat_img_url import parse_product_url, nm_ids

def get_system_info():
    """获取系统信息"""
    cpu_count = psutil.cpu_count()
    memory = psutil.virtual_memory()
    return {
        "cpu_cores": cpu_count,
        "total_memory_gb": round(memory.total / (1024**3), 2),
        "available_memory_gb": round(memory.available / (1024**3), 2),
        "memory_percent": memory.percent
    }

def generate_all_test_urls() -> List[str]:
    """生成所有测试用的图片URL"""
    print(f"📋 生成所有 {len(nm_ids)} 个图片URL...")
    urls = []
    for nm_id in nm_ids:
        img_url = parse_product_url(nm_id, 1)['parse_product_urls'][0]
        urls.append(img_url)
    print(f"✅ 成功生成 {len(urls)} 个URL")
    return urls

async def single_request_with_monitoring(url: str, request_id: int) -> Dict:
    """单个请求测试（带监控）"""
    start_time = time.time()
    
    try:
        from img_describer.service import describe_image
        
        description = await describe_image(url)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 每100个请求打印一次进度
        if request_id % 100 == 0:
            print(f"  📊 已完成 {request_id} 个请求...")
        
        return {
            "request_id": request_id,
            "url": url,
            "success": True,
            "duration": duration,
            "description_length": len(description),
            "error": None
        }
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"  ❌ 请求 {request_id} 失败: {str(e)[:50]}...")
        
        return {
            "request_id": request_id,
            "url": url,
            "success": False,
            "duration": duration,
            "description_length": 0,
            "error": str(e)
        }

async def monitor_system_resources():
    """监控系统资源使用"""
    print("\n🔍 开始系统资源监控...")
    
    while True:
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            print(f"  💻 CPU: {cpu_percent:.1f}% | 内存: {memory.percent:.1f}% ({memory.available/(1024**3):.1f}GB 可用)")
            
            await asyncio.sleep(10)  # 每10秒监控一次
        except asyncio.CancelledError:
            break
        except Exception as e:
            print(f"  ⚠️ 监控异常: {e}")
            break

async def full_concurrent_test(urls: List[str]) -> List[Dict]:
    """全并发测试 - 所有请求同时发起"""
    total_urls = len(urls)
    print(f"\n🚀 开始全并发极限压力测试!")
    print(f"📊 测试规模: {total_urls} 个并发请求")
    print(f"⚡ 并发级别: ALL-IN-ONE (无批次分割)")
    
    # 启动系统监控
    monitor_task = asyncio.create_task(monitor_system_resources())
    
    try:
        # 创建所有并发任务
        print(f"📦 创建 {total_urls} 个并发任务...")
        tasks = []
        for i, url in enumerate(urls):
            task = single_request_with_monitoring(url, i + 1)
            tasks.append(task)
        
        print(f"🎯 所有任务已创建，开始并发执行...")
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行所有并发请求
        print(f"⚡ 发起 {total_urls} 个并发请求...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 记录结束时间
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 停止监控
        monitor_task.cancel()
        
        print(f"\n🎉 全部请求完成!")
        print(f"⏱️  总耗时: {total_duration:.2f}秒")
        print(f"🚀 整体吞吐量: {total_urls/total_duration:.2f} 请求/秒")
        
        # 处理结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, dict):
                valid_results.append(result)
            else:
                # 处理异常
                valid_results.append({
                    "request_id": i + 1,
                    "url": urls[i] if i < len(urls) else "unknown",
                    "success": False,
                    "duration": 0,
                    "description_length": 0,
                    "error": str(result)
                })
        
        return valid_results
        
    except Exception as e:
        monitor_task.cancel()
        print(f"❌ 全并发测试异常: {e}")
        raise

def analyze_extreme_results(results: List[Dict]):
    """分析极限压力测试结果"""
    print(f"\n{'='*100}")
    print("📊 极限压力测试结果分析")
    print('='*100)
    
    total_requests = len(results)
    successful_requests = [r for r in results if r['success']]
    failed_requests = [r for r in results if not r['success']]
    
    success_count = len(successful_requests)
    failure_count = len(failed_requests)
    success_rate = (success_count / total_requests) * 100 if total_requests > 0 else 0
    
    print(f"🎯 极限并发统计:")
    print(f"  总并发请求数: {total_requests}")
    print(f"  成功请求: {success_count} ✅")
    print(f"  失败请求: {failure_count} ❌")
    print(f"  成功率: {success_rate:.2f}%")
    
    if successful_requests:
        durations = [r['duration'] for r in successful_requests]
        desc_lengths = [r['description_length'] for r in successful_requests]
        
        print(f"\n⚡ 极限性能分析:")
        print(f"  最快响应: {min(durations):.2f}秒")
        print(f"  最慢响应: {max(durations):.2f}秒")
        print(f"  平均响应: {statistics.mean(durations):.2f}秒")
        print(f"  中位数响应: {statistics.median(durations):.2f}秒")
        print(f"  响应时间标准差: {statistics.stdev(durations):.2f}秒")
        
        # 性能分布分析
        fast_requests = [d for d in durations if d < 1]  # 缓存命中
        medium_requests = [d for d in durations if 1 <= d < 10]  # 数据库命中
        slow_requests = [d for d in durations if d >= 10]  # 模型请求
        
        print(f"\n🚀 极限响应时间分布:")
        print(f"  超快响应 (<1秒): {len(fast_requests)} ({len(fast_requests)/success_count*100:.1f}%) - 缓存命中")
        print(f"  快速响应 (1-10秒): {len(medium_requests)} ({len(medium_requests)/success_count*100:.1f}%) - 数据库命中")
        print(f"  正常响应 (≥10秒): {len(slow_requests)} ({len(slow_requests)/success_count*100:.1f}%) - 模型请求")
        
        # 百分位数分析
        sorted_durations = sorted(durations)
        p50 = sorted_durations[int(len(sorted_durations) * 0.5)]
        p90 = sorted_durations[int(len(sorted_durations) * 0.9)]
        p95 = sorted_durations[int(len(sorted_durations) * 0.95)]
        p99 = sorted_durations[int(len(sorted_durations) * 0.99)]
        
        print(f"\n📈 响应时间百分位数:")
        print(f"  P50 (中位数): {p50:.2f}秒")
        print(f"  P90: {p90:.2f}秒")
        print(f"  P95: {p95:.2f}秒")
        print(f"  P99: {p99:.2f}秒")
        
        print(f"\n📝 内容质量分析:")
        print(f"  平均描述长度: {statistics.mean(desc_lengths):.0f} 字符")
        print(f"  最短描述: {min(desc_lengths)} 字符")
        print(f"  最长描述: {max(desc_lengths)} 字符")
        print(f"  描述长度标准差: {statistics.stdev(desc_lengths):.0f} 字符")
    
    if failed_requests:
        print(f"\n❌ 失败请求分析:")
        error_types = {}
        for r in failed_requests:
            error = r['error'][:50] + "..." if len(r['error']) > 50 else r['error']
            error_types[error] = error_types.get(error, 0) + 1
        
        for error, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
            print(f"  {error}: {count} 次")

async def main():
    """主测试函数"""
    print("🚀 开始极限并发压力测试...")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示系统信息
    sys_info = get_system_info()
    print(f"\n💻 系统信息:")
    print(f"  CPU 核心数: {sys_info['cpu_cores']}")
    print(f"  总内存: {sys_info['total_memory_gb']} GB")
    print(f"  可用内存: {sys_info['available_memory_gb']} GB")
    print(f"  内存使用率: {sys_info['memory_percent']:.1f}%")
    
    try:
        # 初始化服务
        print(f"\n🔧 初始化服务...")
        from img_describer.service import init_resources
        await init_resources()
        print("✅ 服务初始化完成")
        
        # 生成所有测试URL
        test_urls = generate_all_test_urls()
        
        # 垃圾回收
        gc.collect()
        
        # 执行极限并发测试
        print(f"\n⚠️  警告: 即将发起 {len(test_urls)} 个并发请求!")
        print(f"⚠️  这将对系统造成极大压力，请确保系统资源充足!")
        
        # 等待3秒让用户看到警告
        for i in range(3, 0, -1):
            print(f"⏳ {i} 秒后开始...")
            await asyncio.sleep(1)
        
        results = await full_concurrent_test(test_urls)
        
        # 分析结果
        analyze_extreme_results(results)
        
        print(f"\n🎉 极限压力测试完成!")
        print(f"🏆 系统在 {len(test_urls)} 个并发请求下的表现已完成测试!")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 设置更大的事件循环限制
    if hasattr(asyncio, 'set_event_loop_policy'):
        if sys.platform == 'win32':
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
