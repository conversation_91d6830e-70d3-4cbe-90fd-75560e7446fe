import pytest
import asyncio
import sys
import os
from unittest.mock import AsyncMock, MagicMock

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


@pytest.mark.asyncio
async def test_describe_with_mocks(monkeypatch):
    """测试图片描述功能（使用模拟）"""

    # 模拟 OpenAI 客户端
    async def fake_openai(url):
        return "这是一张测试图片的描述"

    # 模拟 Redis 缓存
    class MockRedisCache:
        def __init__(self):
            self.data = {}

        async def init(self):
            pass

        async def get(self, key):
            return self.data.get(key)

        async def set(self, key, value):
            self.data[key] = value

    # 模拟数据库
    class MockDatabase:
        def __init__(self):
            self.data = {}

        async def init(self):
            pass

        async def fetch_desc(self, url):
            return self.data.get(url)

        async def upsert_desc(self, url, desc):
            self.data[url] = desc

    # 应用模拟
    monkeypatch.setattr("img_describer.openai_client.describe_by_openai", fake_openai)

    # 模拟配置以避免真实 API 调用
    monkeypatch.setattr("img_describer.openai_client._CREDENTIALS", [{"api_key": "test", "base_url": "test", "model": "test"}])

    # 导入并测试
    from img_describer.service import describe_image
    from img_describer import service

    # 替换全局实例
    service.cache = MockRedisCache()
    service.db = MockDatabase()

    await service.cache.init()
    await service.db.init()

    # 测试描述功能
    desc = await describe_image("https://example.com/test.jpg")
    assert desc == "这是一张测试图片的描述"

    # 测试缓存功能
    desc2 = await describe_image("https://example.com/test.jpg")
    assert desc2 == "这是一张测试图片的描述"


@pytest.mark.asyncio
async def test_redis_url_building():
    """测试 Redis URL 构建"""
    from img_describer.config import Settings

    # 测试无密码
    settings = Settings(
        redis_host="localhost",
        redis_port=6379,
        redis_password="",
        redis_db=0,
        redis_url="",
        _env_file=None
    )

    url = settings.get_redis_url()
    assert url == "redis://localhost:6379/0"

    # 测试有密码
    settings.redis_password = "testpass"
    url = settings.get_redis_url()
    assert url == "redis://:testpass@localhost:6379/0"

    # 测试优先使用完整 URL
    settings.redis_url = "redis://:mypass@external:6380/5"
    url = settings.get_redis_url()
    assert url == "redis://:mypass@external:6380/5"
