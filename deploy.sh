#!/bin/bash

# 微服务部署脚本
# 支持分离部署、一体化部署和传统部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "微服务部署脚本"
    echo ""
    echo "用法: $0 [选项] <部署模式>"
    echo ""
    echo "部署模式:"
    echo "  infrastructure    部署基础设施服务（Consul、Ngin<PERSON>、Redis、PostgreSQL）"
    echo "  business         部署业务服务（连接到现有基础设施）"
    echo "  separated        分离部署（先部署基础设施，再部署业务服务）"
    echo "  integrated       一体化部署（使用 docker-compose.consul.yml）"
    echo "  traditional      传统部署（使用 docker-compose.yml）"
    echo ""
    echo "选项:"
    echo "  -h, --help       显示此帮助信息"
    echo "  -v, --verbose    详细输出"
    echo "  -d, --down       停止服务"
    echo "  -r, --reset      重置环境（删除数据卷）"
    echo "  --scale N        扩展业务服务到 N 个实例"
    echo ""
    echo "示例:"
    echo "  $0 separated              # 分离部署（推荐生产环境）"
    echo "  $0 integrated             # 一体化部署"
    echo "  $0 business --scale 3     # 部署业务服务并扩展到3个实例"
    echo "  $0 infrastructure -d      # 停止基础设施服务"
    echo "  $0 integrated -r          # 重置一体化部署环境"
}

# 检查 Docker 和 Docker Compose
check_dependencies() {
    print_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装或不在 PATH 中"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# 检查环境变量文件
check_env_file() {
    local env_file=$1
    local example_file=$2
    
    if [ ! -f "$env_file" ]; then
        if [ -f "$example_file" ]; then
            print_warning "环境变量文件 $env_file 不存在，从 $example_file 复制"
            cp "$example_file" "$env_file"
            print_warning "请编辑 $env_file 文件，设置必要的环境变量（如 OPENAI_API_KEY）"
            return 1
        else
            print_error "环境变量文件 $env_file 和示例文件 $example_file 都不存在"
            exit 1
        fi
    fi
    return 0
}

# 等待服务就绪
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1
    
    print_info "等待 $service_name 服务就绪..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            print_success "$service_name 服务已就绪"
            return 0
        fi
        
        if [ $VERBOSE = true ]; then
            print_info "尝试 $attempt/$max_attempts: $service_name 服务尚未就绪，等待..."
        fi
        
        sleep 5
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name 服务在 $((max_attempts * 5)) 秒内未就绪"
    return 1
}

# 验证部署
verify_deployment() {
    local mode=$1
    
    print_info "验证部署..."
    
    case $mode in
        "infrastructure")
            wait_for_service "Consul" "http://localhost:8500/v1/status/leader"
            wait_for_service "Nginx网关" "http://localhost/gateway/status"
            ;;
        "business"|"separated"|"integrated")
            wait_for_service "Consul" "http://localhost:8500/v1/status/leader"
            wait_for_service "Nginx网关" "http://localhost/gateway/status"
            wait_for_service "业务服务" "http://localhost/api/say-img-description/health"
            ;;
        "traditional")
            wait_for_service "API服务" "http://localhost:8000/health"
            ;;
    esac
    
    print_success "部署验证完成"
}

# 部署基础设施服务
deploy_infrastructure() {
    print_info "部署基础设施服务..."
    
    if [ "$DOWN" = true ]; then
        print_info "停止基础设施服务..."
        docker-compose -f docker-compose.infrastructure.yml down $RESET_FLAG
        print_success "基础设施服务已停止"
        return
    fi
    
    docker-compose -f docker-compose.infrastructure.yml up -d
    print_success "基础设施服务部署完成"
    
    if [ "$VERIFY" = true ]; then
        verify_deployment "infrastructure"
    fi
}

# 部署业务服务
deploy_business() {
    print_info "部署业务服务..."
    
    if [ "$DOWN" = true ]; then
        print_info "停止业务服务..."
        docker-compose -f docker-compose.business.yml down $RESET_FLAG
        print_success "业务服务已停止"
        return
    fi
    
    # 检查环境变量文件
    if ! check_env_file ".env" ".env.business.example"; then
        print_error "请先配置环境变量文件 .env"
        exit 1
    fi
    
    # 检查基础设施是否运行
    if ! curl -s -f "http://localhost:8500/v1/status/leader" > /dev/null 2>&1; then
        print_error "基础设施服务未运行，请先部署基础设施服务"
        print_info "运行: $0 infrastructure"
        exit 1
    fi
    
    local scale_args=""
    if [ -n "$SCALE" ]; then
        scale_args="--scale say-img-description=$SCALE"
    fi
    
    docker-compose -f docker-compose.business.yml up -d $scale_args
    print_success "业务服务部署完成"
    
    if [ -n "$SCALE" ]; then
        print_success "业务服务已扩展到 $SCALE 个实例"
    fi
    
    if [ "$VERIFY" = true ]; then
        verify_deployment "business"
    fi
}

# 分离部署
deploy_separated() {
    print_info "开始分离部署..."
    
    if [ "$DOWN" = true ]; then
        print_info "停止分离部署的服务..."
        docker-compose -f docker-compose.business.yml down $RESET_FLAG
        docker-compose -f docker-compose.infrastructure.yml down $RESET_FLAG
        print_success "分离部署的服务已停止"
        return
    fi
    
    # 部署基础设施
    VERIFY=false deploy_infrastructure
    
    # 等待基础设施就绪
    wait_for_service "Consul" "http://localhost:8500/v1/status/leader"
    wait_for_service "Nginx网关" "http://localhost/gateway/status"
    
    # 部署业务服务
    VERIFY=false deploy_business
    
    print_success "分离部署完成"
    
    if [ "$VERIFY" = true ]; then
        verify_deployment "separated"
    fi
}

# 一体化部署
deploy_integrated() {
    print_info "一体化部署..."
    
    if [ "$DOWN" = true ]; then
        print_info "停止一体化部署的服务..."
        docker-compose -f docker-compose.consul.yml down $RESET_FLAG
        print_success "一体化部署的服务已停止"
        return
    fi
    
    # 检查环境变量文件
    if ! check_env_file ".env" ".env.consul.example"; then
        print_error "请先配置环境变量文件 .env"
        exit 1
    fi
    
    local scale_args=""
    if [ -n "$SCALE" ]; then
        scale_args="--scale say-img-description=$SCALE"
    fi
    
    docker-compose -f docker-compose.consul.yml up -d $scale_args
    print_success "一体化部署完成"
    
    if [ -n "$SCALE" ]; then
        print_success "业务服务已扩展到 $SCALE 个实例"
    fi
    
    if [ "$VERIFY" = true ]; then
        verify_deployment "integrated"
    fi
}

# 传统部署
deploy_traditional() {
    print_info "传统部署..."
    
    if [ "$DOWN" = true ]; then
        print_info "停止传统部署的服务..."
        docker-compose down $RESET_FLAG
        print_success "传统部署的服务已停止"
        return
    fi
    
    # 检查环境变量文件
    if ! check_env_file ".env" ".env.example"; then
        print_error "请先配置环境变量文件 .env"
        exit 1
    fi
    
    docker-compose up -d --build
    print_success "传统部署完成"
    
    if [ "$VERIFY" = true ]; then
        verify_deployment "traditional"
    fi
}

# 主函数
main() {
    # 默认参数
    VERBOSE=false
    DOWN=false
    RESET=false
    VERIFY=true
    SCALE=""
    RESET_FLAG=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -d|--down)
                DOWN=true
                shift
                ;;
            -r|--reset)
                RESET=true
                RESET_FLAG="-v"
                shift
                ;;
            --scale)
                SCALE="$2"
                shift 2
                ;;
            infrastructure|business|separated|integrated|traditional)
                MODE="$1"
                shift
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查部署模式
    if [ -z "$MODE" ]; then
        print_error "请指定部署模式"
        show_help
        exit 1
    fi
    
    # 检查依赖
    check_dependencies
    
    # 执行部署
    case $MODE in
        "infrastructure")
            deploy_infrastructure
            ;;
        "business")
            deploy_business
            ;;
        "separated")
            deploy_separated
            ;;
        "integrated")
            deploy_integrated
            ;;
        "traditional")
            deploy_traditional
            ;;
        *)
            print_error "未知的部署模式: $MODE"
            show_help
            exit 1
            ;;
    esac
    
    if [ "$DOWN" != true ]; then
        print_success "部署完成！"
        print_info "访问地址:"
        case $MODE in
            "infrastructure")
                echo "  - Consul UI: http://localhost:8500"
                echo "  - 网关状态: http://localhost/gateway/status"
                ;;
            "business"|"separated"|"integrated")
                echo "  - API 网关: http://localhost"
                echo "  - Consul UI: http://localhost:8500"
                echo "  - 图片描述服务: http://localhost/api/say-img-description/describe?url=IMAGE_URL"
                ;;
            "traditional")
                echo "  - API 文档: http://localhost:8000/docs"
                echo "  - 图片描述服务: http://localhost:8000/describe?url=IMAGE_URL"
                ;;
        esac
    fi
}

# 运行主函数
main "$@"
