import json
import random
import openai
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
from .config import settings
from .logging import logger, RED

try:
    _CREDENTIALS = json.loads(settings.openai_credentials)
    if not _CREDENTIALS:
        logger.warning(RED + "未检测到 OPENAI_CREDENTIALS，程序可能无法调用 OpenAI 接口！")
except json.JSONDecodeError:
    logger.error(RED + "OPENAI_CREDENTIALS 格式错误，应为 JSON 数组")
    _CREDENTIALS = []


class RateLimitedError(Exception):
    """包装 429 错误"""


@retry(stop=stop_after_attempt(5), wait=wait_fixed(10), retry=retry_if_exception_type(RateLimitedError), reraise=True)
async def describe_by_openai(photo_url: str) -> str:
    if not _CREDENTIALS:
        raise ValueError("没有可用的 OpenAI 凭证")

    cred = random.choice(_CREDENTIALS)
    client = openai.AsyncOpenAI(api_key=cred["api_key"], base_url=cred["base_url"], timeout=settings.openai_timeout)
    try:
        res = await client.chat.completions.create(
            model=cred["model"],
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "image_url", "image_url": {"url": photo_url, "detail": "low"}},
                        {"type": "text", "text": "请用中文描述这张图片的主要内容。"},
                    ],
                }
            ],
        )
    except openai.RateLimitError:
        raise RateLimitedError()
    except Exception as e:
        logger.error(RED + f"OpenAI 请求失败: {e}")
        raise
    return res.choices[0].message.content.strip()
