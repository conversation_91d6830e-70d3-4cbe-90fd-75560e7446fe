#!/usr/bin/env python3
"""直接测试环境变量读取"""
import os
from dotenv import load_dotenv

# 加载 .env 文件
load_dotenv()

print("🔍 直接读取环境变量...")

print("Redis配置:")
print(f"  REDIS_HOST: {os.getenv('REDIS_HOST', 'NOT_SET')}")
print(f"  REDIS_PORT: {os.getenv('REDIS_PORT', 'NOT_SET')}")
print(f"  REDIS_PASSWORD: {os.getenv('REDIS_PASSWORD', 'NOT_SET')}")
print(f"  REDIS_DB: {os.getenv('REDIS_DB', 'NOT_SET')}")
print(f"  REDIS_URL: {os.getenv('REDIS_URL', 'NOT_SET')}")

print("\nPostgreSQL配置:")
print(f"  PG_USER: {os.getenv('PG_USER', 'NOT_SET')}")
print(f"  PG_PASSWORD: {os.getenv('PG_PASSWORD', 'NOT_SET')}")
print(f"  PG_DB: {os.getenv('PG_DB', 'NOT_SET')}")
print(f"  PG_HOST: {os.getenv('PG_HOST', 'NOT_SET')}")
print(f"  PG_PORT: {os.getenv('PG_PORT', 'NOT_SET')}")

print("\nOpenAI配置:")
print(f"  OPENAI_CREDENTIALS: {os.getenv('OPENAI_CREDENTIALS', 'NOT_SET')}")

# 现在测试 pydantic-settings
print("\n" + "="*50)
print("🔍 测试 pydantic-settings...")

import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from pydantic_settings import BaseSettings

class TestSettings(BaseSettings):
    redis_host: str = "default_redis"
    redis_port: int = 6379
    redis_password: str = ""
    redis_db: int = 5
    
    pg_user: str = "default_user"
    pg_password: str = "default_password"
    pg_db: str = "default_db"
    pg_host: str = "default_host"
    pg_port: int = 5432
    
    openai_credentials: str = "[]"
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8"
    }

settings = TestSettings()

print("通过 pydantic-settings 读取:")
print(f"  redis_host: {settings.redis_host}")
print(f"  redis_port: {settings.redis_port}")
print(f"  redis_password: {settings.redis_password}")
print(f"  redis_db: {settings.redis_db}")
print(f"  pg_user: {settings.pg_user}")
print(f"  pg_password: {settings.pg_password}")
print(f"  pg_db: {settings.pg_db}")
print(f"  pg_host: {settings.pg_host}")
print(f"  pg_port: {settings.pg_port}")
print(f"  openai_credentials: {settings.openai_credentials[:50]}...")
