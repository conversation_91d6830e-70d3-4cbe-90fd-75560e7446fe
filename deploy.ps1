# 分离部署架构一键部署脚本
# 用于快速部署整个微服务架构

param(
    [string]$Action = "start",  # start, stop, restart, status
    [switch]$Build = $false,    # 是否重新构建镜像
    [switch]$Clean = $false     # 是否清理资源
)

Write-Host "🚀 分离部署架构管理脚本" -ForegroundColor Green
Write-Host "操作: $Action" -ForegroundColor Yellow

function Show-Status {
    Write-Host "`n📊 服务状态检查..." -ForegroundColor Cyan
    docker ps --format "table {{.Names}}`t{{.Status}}`t{{.Ports}}" | Where-Object { $_ -match "(consul|nginx|say-img|product)" }
}

function Start-Infrastructure {
    Write-Host "`n🏗️  启动基础设施服务..." -ForegroundColor Cyan
    
    # 启动基础设施服务
    docker-compose -f docker-compose.infrastructure.yml up -d
    
    Write-Host "⏳ 等待基础设施服务启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 15
    
    # 检查基础设施服务状态
    Write-Host "🔍 检查基础设施服务状态..." -ForegroundColor Cyan
    $consulHealth = docker exec consul-server consul members 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Consul 服务正常" -ForegroundColor Green
    } else {
        Write-Host "❌ Consul 服务异常" -ForegroundColor Red
    }
    
    # 检查nginx网关
    $nginxStatus = docker exec nginx-gateway nginx -t 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Nginx 网关配置正常" -ForegroundColor Green
    } else {
        Write-Host "❌ Nginx 网关配置异常" -ForegroundColor Red
    }
}

function Start-Business {
    Write-Host "`n💼 启动业务服务..." -ForegroundColor Cyan
    
    if ($Build) {
        Write-Host "🔨 重新构建业务服务镜像..." -ForegroundColor Yellow
        docker-compose -f docker-compose.business.yml build --no-cache
    }
    
    # 启动业务服务
    docker-compose -f docker-compose.business.yml up -d
    
    Write-Host "⏳ 等待业务服务启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    # 检查业务服务健康状态
    Write-Host "🔍 检查业务服务健康状态..." -ForegroundColor Cyan
    try {
        $healthResponse = Invoke-RestMethod -Uri "http://localhost/api/say-img-description/health" -TimeoutSec 10
        if ($healthResponse.status -eq "healthy") {
            Write-Host "✅ 业务服务健康检查通过" -ForegroundColor Green
        } else {
            Write-Host "⚠️  业务服务健康检查异常" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ 业务服务健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Stop-Services {
    Write-Host "`n🛑 停止所有服务..." -ForegroundColor Cyan
    
    # 停止业务服务
    Write-Host "停止业务服务..." -ForegroundColor Yellow
    docker-compose -f docker-compose.business.yml down
    
    # 停止基础设施服务
    Write-Host "停止基础设施服务..." -ForegroundColor Yellow
    docker-compose -f docker-compose.infrastructure.yml down
    
    if ($Clean) {
        Write-Host "🧹 清理Docker资源..." -ForegroundColor Yellow
        docker system prune -f
        docker volume prune -f
        docker network prune -f
    }
}

function Restart-Services {
    Write-Host "`n🔄 重启所有服务..." -ForegroundColor Cyan
    Stop-Services
    Start-Sleep -Seconds 5
    Start-Infrastructure
    Start-Business
}

function Test-Deployment {
    Write-Host "`n🧪 测试部署..." -ForegroundColor Cyan
    
    # 测试基础设施
    Write-Host "测试基础设施服务..." -ForegroundColor Yellow
    try {
        $consulResponse = Invoke-RestMethod -Uri "http://localhost:8500/v1/status/leader" -TimeoutSec 5
        Write-Host "✅ Consul 服务发现正常" -ForegroundColor Green
    } catch {
        Write-Host "❌ Consul 服务发现异常" -ForegroundColor Red
    }
    
    # 测试网关
    try {
        $gatewayResponse = Invoke-RestMethod -Uri "http://localhost/gateway/status" -TimeoutSec 5
        Write-Host "✅ API 网关正常" -ForegroundColor Green
    } catch {
        Write-Host "❌ API 网关异常" -ForegroundColor Red
    }
    
    # 测试业务服务
    try {
        $serviceResponse = Invoke-RestMethod -Uri "http://localhost/api/services" -TimeoutSec 5
        $serviceCount = $serviceResponse.services.Count
        Write-Host "✅ 发现 $serviceCount 个业务服务" -ForegroundColor Green
    } catch {
        Write-Host "❌ 业务服务发现异常" -ForegroundColor Red
    }
}

# 主逻辑
switch ($Action.ToLower()) {
    "start" {
        Start-Infrastructure
        Start-Business
        Show-Status
        Test-Deployment
        
        Write-Host "`n🎉 部署完成！" -ForegroundColor Green
        Write-Host "📋 访问地址:" -ForegroundColor Cyan
        Write-Host "  - API网关: http://localhost" -ForegroundColor White
        Write-Host "  - Consul UI: http://localhost:8500" -ForegroundColor White
        Write-Host "  - 服务发现: http://localhost/api/services" -ForegroundColor White
        Write-Host "  - 图片描述: http://localhost/api/say-img-description/describe?url=IMAGE_URL" -ForegroundColor White
    }
    
    "stop" {
        Stop-Services
        Write-Host "🛑 所有服务已停止" -ForegroundColor Green
    }
    
    "restart" {
        Restart-Services
        Show-Status
        Test-Deployment
        Write-Host "🔄 服务重启完成" -ForegroundColor Green
    }
    
    "status" {
        Show-Status
        Test-Deployment
    }
    
    default {
        Write-Host "❌ 未知操作: $Action" -ForegroundColor Red
        Write-Host "支持的操作: start, stop, restart, status" -ForegroundColor Yellow
        Write-Host "示例:" -ForegroundColor Cyan
        Write-Host "  .\deploy.ps1 start" -ForegroundColor White
        Write-Host "  .\deploy.ps1 restart -Build" -ForegroundColor White
        Write-Host "  .\deploy.ps1 stop -Clean" -ForegroundColor White
    }
}
