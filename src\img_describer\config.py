"""统一管理环境变量与默认配置"""
import os
from typing import Dict, List
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Redis 配置 - 支持两种方式：URL 或分别配置
    redis_url: str = ""  # 如果设置了 URL，优先使用
    redis_host: str = "************"
    redis_port: int = 6379
    redis_password: str = "ls3956573"
    redis_db: int = 5

    # PostgreSQL
    pg_user: str = "lens"
    pg_password: str = "Ls.3956573"
    pg_db: str = "lens"
    pg_host: str = "************"
    pg_port: int = 5432

    # OpenAI / 大模型
    openai_credentials: str = '[{"api_key":"sk-20wUPZL4VpAAPHTW1m6LKlC4jsrlwxLzko8Zbgskk176BVBc","base_url":"http://************:3000/v1","model":"glm-4v-flash"}]'
    openai_timeout: int = 30

    model_config = {
        "env_file": None,  # 暂时禁用 .env 文件读取
        "env_file_encoding": "utf-8"
    }

    def get_redis_url(self) -> str:
        """获取 Redis 连接 URL"""
        if self.redis_url:
            return self.redis_url

        # 构建 Redis URL
        auth_part = f":{self.redis_password}@" if self.redis_password else ""
        return f"redis://{auth_part}{self.redis_host}:{self.redis_port}/{self.redis_db}"


settings = Settings()
