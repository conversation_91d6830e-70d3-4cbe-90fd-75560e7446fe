#!/usr/bin/env python3
"""
验证分离部署的脚本
检查基础设施服务和业务服务是否正常运行
"""

import requests
import time
import sys
import json
from typing import Dict, List, Tuple

# 颜色定义
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

def print_info(message: str):
    print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")

def print_success(message: str):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")

def print_warning(message: str):
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")

def print_error(message: str):
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")

def check_service(name: str, url: str, timeout: int = 5) -> <PERSON><PERSON>[bool, str]:
    """检查服务是否可访问"""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            return True, f"状态码: {response.status_code}"
        else:
            return False, f"状态码: {response.status_code}"
    except requests.exceptions.RequestException as e:
        return False, str(e)

def check_consul_services() -> Tuple[bool, List[str]]:
    """检查 Consul 中注册的服务"""
    try:
        response = requests.get("http://localhost:8500/v1/catalog/services", timeout=5)
        if response.status_code == 200:
            services = list(response.json().keys())
            return True, services
        else:
            return False, []
    except requests.exceptions.RequestException:
        return False, []

def check_service_health(service_name: str) -> Tuple[bool, str]:
    """检查特定服务的健康状态"""
    try:
        url = f"http://localhost:8500/v1/health/service/{service_name}"
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            if health_data:
                # 检查所有实例的健康状态
                healthy_instances = 0
                total_instances = len(health_data)
                
                for instance in health_data:
                    checks = instance.get('Checks', [])
                    all_passing = all(check.get('Status') == 'passing' for check in checks)
                    if all_passing:
                        healthy_instances += 1
                
                status = f"{healthy_instances}/{total_instances} 实例健康"
                return healthy_instances > 0, status
            else:
                return False, "无实例"
        else:
            return False, f"状态码: {response.status_code}"
    except requests.exceptions.RequestException as e:
        return False, str(e)

def test_business_api() -> Tuple[bool, str]:
    """测试业务 API 功能"""
    try:
        # 测试健康检查
        health_url = "http://localhost/api/say-img-description/health"
        response = requests.get(health_url, timeout=10)
        if response.status_code != 200:
            return False, f"健康检查失败: {response.status_code}"
        
        # 测试服务信息
        info_url = "http://localhost/api/say-img-description/info"
        response = requests.get(info_url, timeout=10)
        if response.status_code != 200:
            return False, f"服务信息获取失败: {response.status_code}"
        
        return True, "API 测试通过"
    except requests.exceptions.RequestException as e:
        return False, str(e)

def main():
    """主验证函数"""
    print_info("开始验证分离部署...")
    print("=" * 60)
    
    # 验证项目列表
    checks = [
        ("Consul 服务发现", "http://localhost:8500/v1/status/leader"),
        ("Consul UI", "http://localhost:8500/ui/"),
        ("Nginx 网关", "http://localhost/gateway/status"),
        ("服务发现 API", "http://localhost/api/services"),
    ]
    
    # 基础设施服务检查
    print_info("检查基础设施服务...")
    infrastructure_ok = True
    
    for name, url in checks:
        success, message = check_service(name, url)
        if success:
            print_success(f"✓ {name}: {message}")
        else:
            print_error(f"✗ {name}: {message}")
            infrastructure_ok = False
    
    if not infrastructure_ok:
        print_error("基础设施服务检查失败，请确保基础设施服务正在运行")
        print_info("运行命令: docker-compose -f docker-compose.infrastructure.yml up -d")
        sys.exit(1)
    
    print()
    print_info("检查 Consul 服务注册...")
    
    # 检查 Consul 中的服务
    success, services = check_consul_services()
    if success:
        print_success(f"✓ Consul 服务列表: {', '.join(services)}")
        
        # 检查业务服务是否注册
        business_services = [s for s in services if 'say-img-description' in s]
        if business_services:
            print_success(f"✓ 业务服务已注册: {', '.join(business_services)}")
            
            # 检查业务服务健康状态
            for service in business_services:
                success, status = check_service_health(service)
                if success:
                    print_success(f"✓ {service} 健康状态: {status}")
                else:
                    print_warning(f"⚠ {service} 健康状态: {status}")
        else:
            print_warning("⚠ 未发现业务服务注册，请检查业务服务是否正在运行")
            print_info("运行命令: docker-compose -f docker-compose.business.yml up -d")
    else:
        print_error("✗ 无法获取 Consul 服务列表")
    
    print()
    print_info("测试业务 API...")
    
    # 测试业务 API
    success, message = test_business_api()
    if success:
        print_success(f"✓ 业务 API: {message}")
    else:
        print_error(f"✗ 业务 API: {message}")
        print_info("请检查业务服务是否正在运行并已注册到 Consul")
    
    print()
    print("=" * 60)
    
    # 显示访问地址
    print_info("服务访问地址:")
    print("  - API 网关: http://localhost")
    print("  - Consul UI: http://localhost:8500")
    print("  - 服务发现: http://localhost/api/services")
    print("  - 网关状态: http://localhost/gateway/status")
    print("  - 业务服务健康检查: http://localhost/api/say-img-description/health")
    print("  - 业务服务信息: http://localhost/api/say-img-description/info")
    print("  - 图片描述 API: http://localhost/api/say-img-description/describe?url=IMAGE_URL")
    
    print()
    print_info("测试命令示例:")
    print('  curl "http://localhost/api/say-img-description/describe?url=https://via.placeholder.com/300x200.png?text=Test+Image"')
    
    print()
    if infrastructure_ok and success:
        print_success("✓ 分离部署验证完成，所有服务正常运行！")
        sys.exit(0)
    else:
        print_warning("⚠ 部分服务可能存在问题，请检查日志")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_info("\n验证被用户中断")
        sys.exit(1)
    except Exception as e:
        print_error(f"验证过程中发生错误: {e}")
        sys.exit(1)
