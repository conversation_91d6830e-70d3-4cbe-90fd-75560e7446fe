#!/usr/bin/env python3
"""使用改进版429处理的全系统测试"""
import sys
import os
import asyncio
import time
import statistics
from typing import List, Dict
import psutil

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 导入图片URL生成函数
sys.path.insert(0, os.path.dirname(__file__))
from test_creat_img_url import parse_product_url, nm_ids

def print_header(title: str):
    """打印标题"""
    print(f"\n{'='*80}")
    print(f"🔍 {title}")
    print('='*80)

def get_system_info():
    """获取系统信息"""
    cpu_count = psutil.cpu_count()
    memory = psutil.virtual_memory()
    return {
        "cpu_cores": cpu_count,
        "total_memory_gb": round(memory.total / (1024**3), 2),
        "available_memory_gb": round(memory.available / (1024**3), 2),
        "memory_percent": memory.percent
    }

async def test_basic_functionality():
    """测试基础功能"""
    print_header("基础功能测试")
    
    try:
        from img_describer.service import init_resources, describe_image
        
        # 初始化服务
        await init_resources()
        print("✅ 服务初始化成功")
        
        # 测试单个请求
        test_url = "https://basket-25.wbbasket.ru/vol4491/part449105/449105617/images/big/1.webp"
        print(f"📋 测试URL: {test_url}")
        
        start_time = time.time()
        description = await describe_image(test_url)
        end_time = time.time()
        
        print(f"✅ 基础功能测试成功")
        print(f"⏱️  耗时: {end_time - start_time:.2f}秒")
        print(f"📝 描述长度: {len(description)} 字符")
        print(f"📄 描述内容: {description[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_cache_mechanism():
    """测试缓存机制"""
    print_header("缓存机制测试")
    
    try:
        from img_describer.service import describe_image
        
        test_url = "https://basket-19.wbbasket.ru/vol3130/part313067/313067529/images/big/1.webp"
        
        # 第一次请求 - 应该调用模型
        print("🔄 第一次请求 (预期: 调用模型)")
        start_time = time.time()
        desc1 = await describe_image(test_url)
        time1 = time.time() - start_time
        print(f"  ⏱️  耗时: {time1:.2f}秒")
        
        # 第二次请求 - 应该命中Redis缓存
        print("🔄 第二次请求 (预期: Redis缓存命中)")
        start_time = time.time()
        desc2 = await describe_image(test_url)
        time2 = time.time() - start_time
        print(f"  ⏱️  耗时: {time2:.2f}秒")
        
        # 验证结果
        if desc1 == desc2:
            print("✅ 缓存一致性验证通过")
        else:
            print("❌ 缓存一致性验证失败")
            
        if time2 < 1 and time1 > 3:
            print("✅ 缓存性能验证通过")
        else:
            print("⚠️  缓存性能可能有问题")
            
        return True
        
    except Exception as e:
        print(f"❌ 缓存机制测试失败: {e}")
        return False

async def test_concurrent_performance():
    """测试并发性能"""
    print_header("并发性能测试")
    
    try:
        from img_describer.service import describe_image
        
        # 生成20个不同的测试URL
        test_urls = []
        for i in range(20):
            nm_id = nm_ids[i]
            url = parse_product_url(nm_id, 1)['parse_product_urls'][0]
            test_urls.append(url)
        
        print(f"📋 测试 {len(test_urls)} 个并发请求...")
        
        # 创建并发任务
        async def single_request(url, req_id):
            start_time = time.time()
            try:
                description = await describe_image(url)
                end_time = time.time()
                return {
                    "id": req_id,
                    "success": True,
                    "duration": end_time - start_time,
                    "description_length": len(description),
                    "error": None
                }
            except Exception as e:
                end_time = time.time()
                return {
                    "id": req_id,
                    "success": False,
                    "duration": end_time - start_time,
                    "description_length": 0,
                    "error": str(e)
                }
        
        # 执行并发测试
        tasks = [single_request(url, i+1) for i, url in enumerate(test_urls)]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # 分析结果
        successful = [r for r in results if r['success']]
        failed = [r for r in results if not r['success']]
        
        print(f"📊 并发测试结果:")
        print(f"  总请求数: {len(results)}")
        print(f"  成功请求: {len(successful)} ✅")
        print(f"  失败请求: {len(failed)} ❌")
        print(f"  成功率: {len(successful)/len(results)*100:.1f}%")
        print(f"  总耗时: {total_time:.2f}秒")
        print(f"  吞吐量: {len(successful)/total_time:.2f} 请求/秒")
        
        if successful:
            durations = [r['duration'] for r in successful]
            print(f"  平均响应时间: {statistics.mean(durations):.2f}秒")
            print(f"  最快响应: {min(durations):.2f}秒")
            print(f"  最慢响应: {max(durations):.2f}秒")
        
        if failed:
            print(f"  失败原因:")
            error_types = {}
            for r in failed:
                error = r['error'][:50]
                error_types[error] = error_types.get(error, 0) + 1
            for error, count in error_types.items():
                print(f"    {error}: {count} 次")
        
        return len(successful) > len(failed)
        
    except Exception as e:
        print(f"❌ 并发性能测试失败: {e}")
        return False

async def test_stress_scenario():
    """测试压力场景"""
    print_header("压力场景测试")
    
    try:
        from img_describer.service import describe_image
        
        # 使用50个不同的URL进行压力测试
        test_urls = []
        for i in range(50):
            nm_id = nm_ids[i]
            url = parse_product_url(nm_id, 1)['parse_product_urls'][0]
            test_urls.append(url)
        
        print(f"🚀 压力测试: {len(test_urls)} 个并发请求")
        print("⚠️  这将测试系统的极限处理能力...")
        
        # 监控系统资源
        initial_memory = psutil.virtual_memory().percent
        initial_cpu = psutil.cpu_percent()
        
        async def stress_request(url, req_id):
            try:
                start_time = time.time()
                description = await describe_image(url)
                duration = time.time() - start_time
                
                # 每10个请求报告一次进度
                if req_id % 10 == 0:
                    print(f"  📊 已完成 {req_id} 个请求...")
                
                return {
                    "success": True,
                    "duration": duration,
                    "description_length": len(description)
                }
            except Exception as e:
                return {
                    "success": False,
                    "duration": 0,
                    "error": str(e)
                }
        
        # 执行压力测试
        tasks = [stress_request(url, i+1) for i, url in enumerate(test_urls)]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # 检查系统资源使用
        final_memory = psutil.virtual_memory().percent
        final_cpu = psutil.cpu_percent()
        
        # 分析结果
        successful = [r for r in results if r['success']]
        failed = [r for r in results if not r['success']]
        
        print(f"\n📊 压力测试结果:")
        print(f"  总请求数: {len(results)}")
        print(f"  成功请求: {len(successful)} ✅")
        print(f"  失败请求: {len(failed)} ❌")
        print(f"  成功率: {len(successful)/len(results)*100:.1f}%")
        print(f"  总耗时: {total_time:.2f}秒")
        print(f"  平均吞吐量: {len(successful)/total_time:.2f} 请求/秒")
        
        print(f"\n💻 系统资源使用:")
        print(f"  内存使用: {initial_memory:.1f}% → {final_memory:.1f}%")
        print(f"  CPU使用: {initial_cpu:.1f}% → {final_cpu:.1f}%")
        
        if successful:
            durations = [r['duration'] for r in successful]
            print(f"\n⏱️  响应时间分析:")
            print(f"  平均: {statistics.mean(durations):.2f}秒")
            print(f"  中位数: {statistics.median(durations):.2f}秒")
            print(f"  最快: {min(durations):.2f}秒")
            print(f"  最慢: {max(durations):.2f}秒")
            
            # 响应时间分布
            fast = len([d for d in durations if d < 1])
            medium = len([d for d in durations if 1 <= d < 10])
            slow = len([d for d in durations if d >= 10])
            
            print(f"\n🚀 响应时间分布:")
            print(f"  快速 (<1秒): {fast} ({fast/len(successful)*100:.1f}%)")
            print(f"  中等 (1-10秒): {medium} ({medium/len(successful)*100:.1f}%)")
            print(f"  慢速 (≥10秒): {slow} ({slow/len(successful)*100:.1f}%)")
        
        return len(successful) >= len(results) * 0.8  # 80%成功率算通过
        
    except Exception as e:
        print(f"❌ 压力测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始改进版全系统测试...")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示系统信息
    sys_info = get_system_info()
    print(f"\n💻 系统信息:")
    print(f"  CPU 核心数: {sys_info['cpu_cores']}")
    print(f"  总内存: {sys_info['total_memory_gb']} GB")
    print(f"  可用内存: {sys_info['available_memory_gb']} GB")
    print(f"  内存使用率: {sys_info['memory_percent']:.1f}%")
    
    # 执行测试套件
    test_results = {}
    
    # 1. 基础功能测试
    test_results['basic'] = await test_basic_functionality()
    
    # 2. 缓存机制测试
    test_results['cache'] = await test_cache_mechanism()
    
    # 3. 并发性能测试
    test_results['concurrent'] = await test_concurrent_performance()
    
    # 4. 压力场景测试
    test_results['stress'] = await test_stress_scenario()
    
    # 总结测试结果
    print_header("测试结果总结")
    
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    print(f"📊 测试套件结果:")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        test_display = {
            'basic': '基础功能测试',
            'cache': '缓存机制测试', 
            'concurrent': '并发性能测试',
            'stress': '压力场景测试'
        }
        print(f"  {test_display[test_name]}: {status}")
    
    print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！改进版系统运行完美！")
        print("\n✨ 改进版优势:")
        print("  - ⚡ 智能429错误处理")
        print("  - 🚀 指数退避重试策略")
        print("  - 🎯 API凭证智能管理")
        print("  - 📊 请求队列并发控制")
        print("  - 💾 完整的缓存机制")
        print("  - 🔄 高并发处理能力")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} 个测试失败，需要进一步优化")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
