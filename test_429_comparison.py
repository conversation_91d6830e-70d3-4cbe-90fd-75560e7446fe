#!/usr/bin/env python3
"""对比原版和改进版的429错误处理效果"""
import sys
import os
import asyncio
import time
from typing import List, Dict

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_original_429_handling():
    """测试原版429处理"""
    print("🔍 测试原版429错误处理机制...")
    
    try:
        from img_describer.openai_client import describe_by_openai
        
        # 使用多个不同的URL来增加触发429的可能性
        test_urls = [
            "https://basket-25.wbbasket.ru/vol4491/part449105/449105617/images/big/1.webp",
            "https://basket-19.wbbasket.ru/vol3130/part313067/313067529/images/big/1.webp",
            "https://basket-22.wbbasket.ru/vol3767/part376736/376736954/images/big/1.webp",
        ]
        
        results = []
        start_time = time.time()
        
        # 快速连续请求
        for i, url in enumerate(test_urls):
            print(f"  📋 原版请求 {i+1}/{len(test_urls)}")
            req_start = time.time()
            
            try:
                description = await describe_by_openai(url)
                req_end = time.time()
                
                results.append({
                    "success": True,
                    "duration": req_end - req_start,
                    "description_length": len(description)
                })
                print(f"    ✅ 成功 (耗时: {req_end - req_start:.2f}秒)")
                
            except Exception as e:
                req_end = time.time()
                results.append({
                    "success": False,
                    "duration": req_end - req_start,
                    "error": str(e)
                })
                print(f"    ❌ 失败 (耗时: {req_end - req_start:.2f}秒): {str(e)[:50]}...")
        
        total_time = time.time() - start_time
        
        return {
            "version": "原版",
            "results": results,
            "total_time": total_time,
            "success_count": sum(1 for r in results if r['success']),
            "total_requests": len(results)
        }
        
    except Exception as e:
        print(f"❌ 原版测试异常: {e}")
        return None

async def test_improved_429_handling():
    """测试改进版429处理"""
    print("\n🚀 测试改进版429错误处理机制...")
    
    try:
        from img_describer.openai_client_improved import describe_by_openai_with_queue
        
        # 使用相同的URL进行对比
        test_urls = [
            "https://basket-22.wbbasket.ru/vol3875/part387577/387577046/images/big/1.webp",
            "https://basket-10.wbbasket.ru/vol1488/part148846/148846766/images/big/1.webp",
            "https://basket-16.wbbasket.ru/vol2431/part243143/243143892/images/big/1.webp",
        ]
        
        results = []
        start_time = time.time()
        
        # 快速连续请求
        for i, url in enumerate(test_urls):
            print(f"  📋 改进版请求 {i+1}/{len(test_urls)}")
            req_start = time.time()
            
            try:
                description = await describe_by_openai_with_queue(url)
                req_end = time.time()
                
                results.append({
                    "success": True,
                    "duration": req_end - req_start,
                    "description_length": len(description)
                })
                print(f"    ✅ 成功 (耗时: {req_end - req_start:.2f}秒)")
                
            except Exception as e:
                req_end = time.time()
                results.append({
                    "success": False,
                    "duration": req_end - req_start,
                    "error": str(e)
                })
                print(f"    ❌ 失败 (耗时: {req_end - req_start:.2f}秒): {str(e)[:50]}...")
        
        total_time = time.time() - start_time
        
        return {
            "version": "改进版",
            "results": results,
            "total_time": total_time,
            "success_count": sum(1 for r in results if r['success']),
            "total_requests": len(results)
        }
        
    except Exception as e:
        print(f"❌ 改进版测试异常: {e}")
        return None

def compare_results(original_result: Dict, improved_result: Dict):
    """对比测试结果"""
    print(f"\n{'='*80}")
    print("📊 429错误处理机制对比分析")
    print('='*80)
    
    if not original_result or not improved_result:
        print("❌ 无法进行对比，部分测试失败")
        return
    
    print(f"📈 整体性能对比:")
    print(f"  {'版本':<10} {'成功率':<10} {'总耗时':<10} {'平均耗时':<10}")
    print(f"  {'-'*40}")
    
    for result in [original_result, improved_result]:
        success_rate = (result['success_count'] / result['total_requests']) * 100
        avg_time = result['total_time'] / result['total_requests']
        
        print(f"  {result['version']:<10} {success_rate:>6.1f}%   {result['total_time']:>6.2f}s   {avg_time:>6.2f}s")
    
    print(f"\n🔍 详细分析:")
    
    # 成功率对比
    orig_success_rate = (original_result['success_count'] / original_result['total_requests']) * 100
    impr_success_rate = (improved_result['success_count'] / improved_result['total_requests']) * 100
    
    if impr_success_rate > orig_success_rate:
        print(f"  ✅ 改进版成功率更高: {impr_success_rate:.1f}% vs {orig_success_rate:.1f}%")
    elif impr_success_rate < orig_success_rate:
        print(f"  ⚠️  原版成功率更高: {orig_success_rate:.1f}% vs {impr_success_rate:.1f}%")
    else:
        print(f"  ➡️  成功率相同: {orig_success_rate:.1f}%")
    
    # 总耗时对比
    if improved_result['total_time'] < original_result['total_time']:
        time_saved = original_result['total_time'] - improved_result['total_time']
        print(f"  ⚡ 改进版更快: 节省 {time_saved:.2f}秒")
    elif improved_result['total_time'] > original_result['total_time']:
        time_added = improved_result['total_time'] - original_result['total_time']
        print(f"  🐌 改进版更慢: 增加 {time_added:.2f}秒")
    else:
        print(f"  ➡️  耗时相同")
    
    # 错误分析
    orig_errors = [r for r in original_result['results'] if not r['success']]
    impr_errors = [r for r in improved_result['results'] if not r['success']]
    
    if orig_errors:
        print(f"\n❌ 原版错误:")
        for i, error in enumerate(orig_errors):
            print(f"    {i+1}. {error['error'][:60]}...")
    
    if impr_errors:
        print(f"\n❌ 改进版错误:")
        for i, error in enumerate(impr_errors):
            print(f"    {i+1}. {error['error'][:60]}...")

def show_improvement_summary():
    """显示改进总结"""
    print(f"\n{'='*80}")
    print("💡 429错误处理改进总结")
    print('='*80)
    
    print("🎯 当前实现的改进:")
    print("  ✅ 指数退避策略: 2, 4, 8, 16, 32秒递增等待")
    print("  ✅ API凭证管理: 自动阻塞和轮换有问题的凭证")
    print("  ✅ 响应头解析: 根据Retry-After头调整等待时间")
    print("  ✅ 请求队列: 控制最大并发数量(20个)")
    print("  ✅ 智能重试: 只对429错误进行重试")
    
    print("\n🚀 性能优势:")
    print("  - 减少无效等待时间")
    print("  - 提高API凭证利用率")
    print("  - 避免雪崩式失败")
    print("  - 更好的并发控制")
    
    print("\n⚡ 使用建议:")
    print("  - 高并发场景使用改进版")
    print("  - 可根据实际情况调整队列大小")
    print("  - 监控API凭证使用情况")
    print("  - 考虑添加更多API凭证")

async def main():
    """主函数"""
    print("🚀 开始429错误处理机制对比测试...")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 初始化服务
    try:
        from img_describer.service import init_resources
        await init_resources()
        print("✅ 服务初始化完成")
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        return False
    
    # 测试原版
    original_result = await test_original_429_handling()
    
    # 等待一下避免请求过快
    await asyncio.sleep(2)
    
    # 测试改进版
    improved_result = await test_improved_429_handling()
    
    # 对比结果
    compare_results(original_result, improved_result)
    
    # 显示改进总结
    show_improvement_summary()
    
    print(f"\n🎉 429错误处理对比测试完成!")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
