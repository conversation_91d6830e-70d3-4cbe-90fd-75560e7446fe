#!/bin/bash
# 快速部署脚本 - 使用最新修复版本
# 用于快速部署整个微服务架构

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 默认参数
ACTION="start"
BUILD=false
CLEAN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        start|stop|restart|status)
            ACTION="$1"
            shift
            ;;
        --build)
            BUILD=true
            shift
            ;;
        --clean)
            CLEAN=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [start|stop|restart|status] [--build] [--clean]"
            echo "  start   - 启动所有服务"
            echo "  stop    - 停止所有服务"
            echo "  restart - 重启所有服务"
            echo "  status  - 查看服务状态"
            echo "  --build - 重新构建镜像"
            echo "  --clean - 清理Docker资源"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

echo -e "${GREEN}🚀 快速部署脚本 - 最新修复版本${NC}"
echo -e "${YELLOW}操作: $ACTION${NC}"

show_status() {
    echo -e "\n${CYAN}📊 服务状态检查...${NC}"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(consul|nginx|say-img|product)" || true
}

start_infrastructure() {
    echo -e "\n${CYAN}🏗️  启动基础设施服务...${NC}"
    
    # 启动基础设施服务
    docker-compose -f docker-compose.infrastructure.yml up -d
    
    echo -e "${YELLOW}⏳ 等待基础设施服务启动...${NC}"
    sleep 15
    
    # 检查基础设施服务状态
    echo -e "${CYAN}🔍 检查基础设施服务状态...${NC}"
    if docker exec consul-server consul members >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Consul 服务正常${NC}"
    else
        echo -e "${RED}❌ Consul 服务异常${NC}"
    fi
    
    # 检查nginx网关
    if docker exec nginx-gateway nginx -t >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Nginx 网关配置正常${NC}"
    else
        echo -e "${RED}❌ Nginx 网关配置异常${NC}"
    fi
    
    # 重启consul-template确保配置正确生成
    echo -e "${YELLOW}🔄 重启consul-template确保配置正确...${NC}"
    docker restart consul-template
    sleep 8
    
    # 重新加载nginx配置
    echo -e "${YELLOW}🔄 重新加载nginx配置...${NC}"
    docker exec nginx-gateway nginx -s reload
}

start_business() {
    echo -e "\n${CYAN}💼 启动业务服务...${NC}"
    
    if [ "$BUILD" = true ]; then
        echo -e "${YELLOW}🔨 重新构建业务服务镜像...${NC}"
        docker-compose -f docker-compose.business.yml build --no-cache
    fi
    
    # 启动业务服务
    docker-compose -f docker-compose.business.yml up -d
    
    echo -e "${YELLOW}⏳ 等待业务服务启动...${NC}"
    sleep 10
    
    # 检查业务服务健康状态
    echo -e "${CYAN}🔍 检查业务服务健康状态...${NC}"
    if curl -s -f "http://localhost/api/say-img-description/health" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 业务服务健康检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️  等待服务完全启动...${NC}"
        sleep 5
        if curl -s -f "http://localhost/api/say-img-description/health" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ 业务服务健康检查通过${NC}"
        else
            echo -e "${RED}❌ 业务服务健康检查失败${NC}"
        fi
    fi
}

stop_services() {
    echo -e "\n${CYAN}🛑 停止所有服务...${NC}"
    
    # 停止业务服务
    echo -e "${YELLOW}停止业务服务...${NC}"
    docker-compose -f docker-compose.business.yml down
    
    # 停止基础设施服务
    echo -e "${YELLOW}停止基础设施服务...${NC}"
    docker-compose -f docker-compose.infrastructure.yml down
    
    if [ "$CLEAN" = true ]; then
        echo -e "${YELLOW}🧹 清理Docker资源...${NC}"
        docker system prune -f
        docker volume prune -f
        docker network prune -f
    fi
}

restart_services() {
    echo -e "\n${CYAN}🔄 重启所有服务...${NC}"
    stop_services
    sleep 5
    start_infrastructure
    start_business
}

test_deployment() {
    echo -e "\n${CYAN}🧪 测试部署...${NC}"
    
    # 测试基础设施
    echo -e "${YELLOW}测试基础设施服务...${NC}"
    if curl -s -f "http://localhost:8500/v1/status/leader" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Consul 服务发现正常${NC}"
    else
        echo -e "${RED}❌ Consul 服务发现异常${NC}"
    fi
    
    # 测试网关
    if curl -s -f "http://localhost/gateway/status" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ API 网关正常${NC}"
    else
        echo -e "${RED}❌ API 网关异常${NC}"
    fi
    
    # 测试业务服务
    if curl -s -f "http://localhost/api/services" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 业务服务发现正常${NC}"
    else
        echo -e "${RED}❌ 业务服务发现异常${NC}"
    fi
    
    # 测试图片描述服务
    echo -e "${YELLOW}测试图片描述服务...${NC}"
    if curl -s -f "http://localhost/api/say-img-description/health" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 图片描述服务正常${NC}"
    else
        echo -e "${RED}❌ 图片描述服务异常${NC}"
    fi
}

# 主逻辑
case $ACTION in
    "start")
        start_infrastructure
        start_business
        show_status
        test_deployment
        
        echo -e "\n${GREEN}🎉 部署完成！${NC}"
        echo -e "${CYAN}📋 访问地址:${NC}"
        echo -e "  - API网关: http://localhost"
        echo -e "  - Consul UI: http://localhost:8500"
        echo -e "  - 服务发现: http://localhost/api/services"
        echo -e "  - 图片描述: http://localhost/api/say-img-description/describe?url=IMAGE_URL"
        echo -e "\n${CYAN}🧪 测试命令:${NC}"
        echo -e "  curl \"http://localhost/api/say-img-description/describe?url=https://example.com/image.jpg\""
        ;;
    
    "stop")
        stop_services
        echo -e "${GREEN}🛑 所有服务已停止${NC}"
        ;;
    
    "restart")
        restart_services
        show_status
        test_deployment
        echo -e "${GREEN}🔄 服务重启完成${NC}"
        ;;
    
    "status")
        show_status
        test_deployment
        ;;
    
    *)
        echo -e "${RED}❌ 未知操作: $ACTION${NC}"
        echo -e "${YELLOW}支持的操作: start, stop, restart, status${NC}"
        echo -e "${CYAN}示例:${NC}"
        echo -e "  ./quick-deploy.sh start"
        echo -e "  ./quick-deploy.sh restart --build"
        echo -e "  ./quick-deploy.sh stop --clean"
        exit 1
        ;;
esac
