#!/usr/bin/env python3
"""简化版403错误重试测试"""
import sys
import os
import asyncio
import time

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_openai_client_directly():
    """直接测试OpenAI客户端的403错误处理"""
    print("🔍 直接测试OpenAI客户端的403错误处理...")
    print("="*80)
    
    try:
        # 导入OpenAI客户端
        from img_describer.openai_client_improved import describe_by_openai_improved
        from img_describer.openai_client import describe_by_openai
        
        # 使用真实的产品ID生成图片URL
        from test_creat_img_url import parse_product_url, nm_ids
        
        # 选择一个真实的产品ID
        test_product_id = nm_ids[0]  # "449105617"
        test_url = parse_product_url(test_product_id, 1)['parse_product_urls'][0]
        
        print(f"📋 测试产品ID: {test_product_id}")
        print(f"📋 测试URL: {test_url}")
        print()
        
        # 测试1: 改进版客户端
        print("🧪 测试1: 改进版OpenAI客户端")
        print("-" * 50)
        
        start_time = time.time()
        try:
            result = await describe_by_openai_improved(test_url)
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ 成功! 耗时: {duration:.2f}秒")
            print(f"📝 描述: {result[:100]}...")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"❌ 失败! 耗时: {duration:.2f}秒")
            print(f"🚫 错误类型: {type(e).__name__}")
            print(f"🚫 错误信息: {str(e)}")
            
            # 分析重试行为
            if "403" in str(e) or "User disabled" in str(e):
                print(f"🔍 检测到403错误:")
                if duration < 2:
                    print(f"   ❌ 立即失败，没有重试")
                else:
                    print(f"   🔄 可能进行了重试 (耗时{duration:.1f}秒)")
        
        print()
        
        # 测试2: 原始客户端
        print("🧪 测试2: 原始OpenAI客户端")
        print("-" * 50)
        
        start_time = time.time()
        try:
            result = await describe_by_openai(test_url)
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ 成功! 耗时: {duration:.2f}秒")
            print(f"📝 描述: {result[:100]}...")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"❌ 失败! 耗时: {duration:.2f}秒")
            print(f"🚫 错误类型: {type(e).__name__}")
            print(f"🚫 错误信息: {str(e)}")
            
            # 分析重试行为
            if "403" in str(e) or "User disabled" in str(e):
                print(f"🔍 检测到403错误:")
                if duration < 2:
                    print(f"   ❌ 立即失败，没有重试")
                else:
                    print(f"   🔄 可能进行了重试 (耗时{duration:.1f}秒)")
        
        print()
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

def analyze_code_logic():
    """分析代码逻辑"""
    print("🔍 分析代码逻辑...")
    print("="*80)
    
    print("📋 重试机制分析:")
    print()
    
    print("1. 🎯 重试条件:")
    print("   - 两个客户端都使用: retry_if_exception_type(RateLimitedError)")
    print("   - 只有RateLimitedError类型的异常才会重试")
    print("   - 其他所有异常都会直接抛出")
    print()
    
    print("2. 🎯 异常处理:")
    print("   - openai.RateLimitError -> 转换为RateLimitedError (会重试)")
    print("   - 其他Exception -> 直接抛出 (不会重试)")
    print("   - 403错误属于其他Exception，不会重试")
    print()
    
    print("3. 🚨 关键结论:")
    print("   - 403 'User disabled' 错误不会触发重试")
    print("   - 系统会立即失败，不会尝试其他API密钥")
    print("   - 这是当前系统的一个重要缺陷")
    print()

async def main():
    """主函数"""
    print("🚀 开始403错误重试行为调查...")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 分析代码逻辑
    analyze_code_logic()
    
    # 直接测试OpenAI客户端
    await test_openai_client_directly()
    
    print("🎉 403错误重试行为调查完成!")
    print()
    print("📊 调查结果总结:")
    print("   ✅ 确认403错误不会触发重试机制")
    print("   ✅ 系统会立即失败，不会切换API密钥")
    print("   ⚠️  这解释了为什么经常出现403错误")
    print("   💡 需要改进错误处理机制来解决此问题")

if __name__ == "__main__":
    asyncio.run(main())
