#!/usr/bin/env python3
"""测试指定图片的描述功能"""
import requests
import time
import json

def test_image_description():
    """测试图片描述功能"""
    test_url = "https://basket-11.wbbasket.ru/vol1614/part161483/161483769/images/big/1.webp"
    api_url = f"http://localhost/api/say-img-description/describe?url={test_url}"

    # 同时测试直接访问业务服务
    direct_url = f"http://localhost:8000/describe?url={test_url}"
    
    print("🔍 测试图片描述服务...")
    print(f"📷 测试图片URL: {test_url}")
    print(f"🌐 通过网关访问: {api_url}")
    print(f"🔗 直接访问服务: {direct_url}")
    print("-" * 60)

    # 先测试直接访问
    print("📋 测试1: 直接访问业务服务")
    try:
        start_time = time.time()
        response = requests.get(direct_url, timeout=30)
        end_time = time.time()

        print(f"📊 请求状态码: {response.status_code}")
        print(f"⏱️  响应时间: {end_time - start_time:.2f}秒")

        if response.status_code == 200:
            result = response.json()
            print(f"✅ 直接访问成功!")
            print(f"🖼️  图片URL: {result.get('url', 'N/A')}")
            print(f"📝 图片描述:")
            print(f"   {result.get('description', 'N/A')}")
        else:
            print(f"❌ 直接访问失败!")
            print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 直接访问发生错误: {e}")

    print("\n" + "=" * 60)
    print("📋 测试2: 通过网关访问")
    
    try:
        start_time = time.time()
        response = requests.get(api_url, timeout=30)
        end_time = time.time()
        
        print(f"📊 请求状态码: {response.status_code}")
        print(f"⏱️  响应时间: {end_time - start_time:.2f}秒")
        
        print(f"📄 原始响应内容: {response.text}")
        print(f"📋 响应头: {dict(response.headers)}")

        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ 请求成功!")
                print(f"🖼️  图片URL: {result.get('url', 'N/A')}")
                print(f"📝 图片描述:")
                print(f"   {result.get('description', 'N/A')}")
            except json.JSONDecodeError:
                print(f"❌ JSON解析失败!")
                print(f"响应内容: {response.text}")
        else:
            print(f"❌ 请求失败!")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时!")
    except requests.exceptions.ConnectionError:
        print("🔌 连接错误!")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    test_image_description()
