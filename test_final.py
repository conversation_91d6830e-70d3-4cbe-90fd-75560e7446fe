#!/usr/bin/env python3
"""最终测试总结"""
import sys
import os
import asyncio
import time

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

def print_result(success, message):
    """打印结果"""
    icon = "✅" if success else "❌"
    print(f"{icon} {message}")

async def test_configuration():
    """测试配置"""
    print_header("配置测试")
    
    try:
        from img_describer.config import settings
        
        # 验证关键配置
        redis_ok = settings.redis_host == "************" and settings.redis_password == "ls3956573"
        pg_ok = settings.pg_host == "************" and settings.pg_user == "lens"
        openai_ok = "glm-4v-flash" in settings.openai_credentials
        
        print_result(redis_ok, f"Redis 配置: {settings.redis_host}:{settings.redis_port}")
        print_result(pg_ok, f"PostgreSQL 配置: {settings.pg_user}@{settings.pg_host}:{settings.pg_port}/{settings.pg_db}")
        print_result(openai_ok, f"OpenAI 配置: 已配置 GLM-4V-Flash 模型")
        
        return redis_ok and pg_ok and openai_ok
        
    except Exception as e:
        print_result(False, f"配置测试失败: {e}")
        return False

async def test_services():
    """测试服务连接"""
    print_header("服务连接测试")
    
    try:
        from img_describer.service import init_resources
        
        print("正在初始化服务资源...")
        await init_resources()
        
        print_result(True, "Redis 连接成功")
        print_result(True, "PostgreSQL 连接成功")
        print_result(True, "服务初始化完成")
        
        return True
        
    except Exception as e:
        print_result(False, f"服务连接失败: {e}")
        return False

async def test_image_description():
    """测试图片描述功能"""
    print_header("图片描述功能测试")
    
    try:
        from img_describer.service import describe_image
        
        # 使用一个测试图片
        test_url = "https://basket-25.wbbasket.ru/vol4491/part449105/449105617/images/big/1.webp"
        
        print(f"测试图片: {test_url}")
        print("正在生成描述...")
        
        start_time = time.time()
        description = await describe_image(test_url)
        end_time = time.time()
        
        duration = end_time - start_time
        
        print_result(True, f"描述生成成功 (耗时: {duration:.2f}秒)")
        print(f"📝 描述内容: {description[:100]}...")
        
        return True
        
    except Exception as e:
        print_result(False, f"图片描述失败: {e}")
        return False

def test_api_status():
    """测试 API 状态"""
    print_header("API 服务状态")
    
    try:
        import requests
        
        # 测试 API 文档
        docs_response = requests.get("http://localhost:8000/docs", timeout=5)
        docs_ok = docs_response.status_code == 200
        print_result(docs_ok, f"API 文档访问: http://localhost:8000/docs")
        
        # 测试 API 接口
        test_url = "https://basket-25.wbbasket.ru/vol4491/part449105/449105617/images/big/1.webp"
        api_response = requests.get(f"http://localhost:8000/describe?url={test_url}", timeout=30)
        api_ok = api_response.status_code == 200
        print_result(api_ok, f"API 接口调用: /describe")
        
        if api_ok:
            result = api_response.json()
            print(f"📝 API 返回描述: {result.get('description', 'N/A')[:100]}...")
        
        return docs_ok and api_ok
        
    except Exception as e:
        print_result(False, f"API 测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始最终测试...")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行所有测试
    config_ok = await test_configuration()
    services_ok = await test_services()
    description_ok = await test_image_description()
    api_ok = test_api_status()
    
    # 总结
    print_header("测试总结")
    
    tests = [
        ("配置测试", config_ok),
        ("服务连接", services_ok),
        ("图片描述", description_ok),
        ("API 服务", api_ok)
    ]
    
    passed = sum(1 for _, ok in tests if ok)
    total = len(tests)
    
    for name, ok in tests:
        print_result(ok, name)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！项目运行正常。")
        print("\n🌐 服务地址:")
        print("  - API 文档: http://localhost:8000/docs")
        print("  - API 接口: http://localhost:8000/describe?url=图片URL")
        print("\n✨ 功能特性:")
        print("  - ✅ Redis 缓存加速")
        print("  - ✅ PostgreSQL 持久化存储")
        print("  - ✅ GLM-4V-Flash 图片理解")
        print("  - ✅ FastAPI RESTful 接口")
        print("  - ✅ 异步高并发处理")
        return True
    else:
        print(f"\n❌ {total - passed} 个测试失败，请检查配置和服务状态。")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
