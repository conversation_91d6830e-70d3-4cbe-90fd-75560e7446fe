#!/usr/bin/env python3
"""
启动支持 Consul 服务发现的 API 服务
"""
import sys
import os
import signal
import asyncio
from typing import Optional

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 导入日志模块
from img_describer.logging import logger, GREEN, RED


class ConsulAPIServer:
    """Consul API 服务器管理类"""
    
    def __init__(self):
        self.server: Optional[object] = None
        self.should_exit = False
        
    async def start_server(self):
        """启动服务器"""
        try:
            import uvicorn
            from img_describer.api import app
            
            # 服务器配置
            host = os.getenv("HOST", "0.0.0.0")
            port = int(os.getenv("SERVICE_PORT", "8000"))
            
            logger.info(GREEN + f"✓ 启动 Consul API 服务器: {host}:{port}")
            
            # 创建 uvicorn 配置
            config = uvicorn.Config(
                app=app,
                host=host,
                port=port,
                log_level="info",
                access_log=True,
                reload=False  # 生产环境不使用热重载
            )
            
            # 创建服务器实例
            server = uvicorn.Server(config)
            self.server = server
            
            # 设置信号处理
            self._setup_signal_handlers()
            
            # 启动服务器
            await server.serve()
            
        except Exception as e:
            logger.error(RED + f"✗ 服务器启动失败: {e}")
            sys.exit(1)
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，准备关闭服务器...")
            self.should_exit = True
            if self.server:
                self.server.should_exit = True
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # Docker stop
        
        if hasattr(signal, 'SIGHUP'):  # Unix 系统
            signal.signal(signal.SIGHUP, signal_handler)
    
    async def wait_for_shutdown(self):
        """等待关闭信号"""
        while not self.should_exit:
            await asyncio.sleep(0.1)


async def main():
    """主函数"""
    logger.info(GREEN + "=" * 50)
    logger.info(GREEN + "启动图片描述服务 (Consul 模式)")
    logger.info(GREEN + "=" * 50)
    
    # 检查必要的环境变量
    required_env_vars = ["SERVICE_NAME", "CONSUL_HOST"]
    missing_vars = []
    
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(RED + f"✗ 缺少必要的环境变量: {', '.join(missing_vars)}")
        logger.error(RED + "请检查 .env 文件或环境变量配置")
        sys.exit(1)
    
    # 显示配置信息
    logger.info(f"服务名称: {os.getenv('SERVICE_NAME')}")
    logger.info(f"服务端口: {os.getenv('SERVICE_PORT', '8000')}")
    logger.info(f"Consul 地址: {os.getenv('CONSUL_HOST')}:{os.getenv('CONSUL_PORT', '8500')}")
    logger.info(f"服务标签: {os.getenv('SERVICE_TAGS', 'api')}")
    logger.info(f"环境: {os.getenv('ENVIRONMENT', 'development')}")
    
    # 创建并启动服务器
    server = ConsulAPIServer()
    
    try:
        await server.start_server()
    except KeyboardInterrupt:
        logger.info("收到键盘中断信号")
    except Exception as e:
        logger.error(RED + f"✗ 服务运行异常: {e}")
        sys.exit(1)
    finally:
        logger.info(GREEN + "✓ 服务已关闭")


def check_dependencies():
    """检查依赖项"""
    try:
        import uvicorn
        import aiohttp
        import fastapi
        logger.info(GREEN + "✓ 依赖检查通过")
        return True
    except ImportError as e:
        logger.error(RED + f"✗ 缺少依赖: {e}")
        logger.error(RED + "请运行: pip install -r requirements.txt")
        return False


if __name__ == "__main__":
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 运行主程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(RED + f"✗ 程序异常退出: {e}")
        sys.exit(1)
