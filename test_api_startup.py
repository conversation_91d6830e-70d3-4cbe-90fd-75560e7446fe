#!/usr/bin/env python3
"""测试 API 启动"""
import sys
import os
import asyncio

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_api_import():
    """测试 API 模块导入"""
    print("=== 测试 API 模块导入 ===")
    
    try:
        from img_describer.api import app
        print("✓ API 模块导入成功")
        print(f"  应用标题: {app.title}")
        
        # 测试路由
        routes = [route.path for route in app.routes]
        print(f"  可用路由: {routes}")
        
        return True
        
    except Exception as e:
        print(f"❌ API 导入错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_service_import():
    """测试服务模块导入"""
    print("\n=== 测试服务模块导入 ===")
    
    try:
        from img_describer.service import describe_image
        print("✓ 服务模块导入成功")
        
        # 测试 OpenAI 客户端导入
        from img_describer.openai_client import describe_by_openai
        print("✓ OpenAI 客户端模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务导入错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🔍 开始测试 API 和服务模块...")
    
    success1 = await test_api_import()
    success2 = await test_service_import()
    
    if success1 and success2:
        print("\n🎉 所有模块导入测试通过！")
        print("\n📋 下一步可以:")
        print("1. 配置 .env 文件中的 OpenAI 凭证")
        print("2. 启动 Redis 和 PostgreSQL 服务")
        print("3. 运行 'python run_api.py' 启动 API 服务")
        return True
    else:
        print("\n❌ 模块导入测试失败")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
