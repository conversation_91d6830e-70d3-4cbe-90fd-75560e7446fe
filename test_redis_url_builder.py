#!/usr/bin/env python3
"""测试 Redis URL 构建逻辑"""

def build_redis_url(redis_url="", redis_host="redis", redis_port=6379, redis_password="", redis_db=5):
    """构建 Redis URL - 复制配置类中的逻辑"""
    if redis_url:
        return redis_url
    
    # 构建 Redis URL
    auth_part = f":{redis_password}@" if redis_password else ""
    return f"redis://{auth_part}{redis_host}:{redis_port}/{redis_db}"

def test_redis_url_building():
    """测试 Redis URL 构建"""
    print("=== 测试 Redis URL 构建逻辑 ===")
    
    # 测试1: 有密码
    print("\n1. 测试有密码配置:")
    url1 = build_redis_url(
        redis_host="localhost",
        redis_port=6379,
        redis_password="mypassword",
        redis_db=5
    )
    print(f"  结果: {url1}")
    assert url1 == "redis://:mypassword@localhost:6379/5"
    
    # 测试2: 无密码
    print("\n2. 测试无密码配置:")
    url2 = build_redis_url(
        redis_host="redis-server",
        redis_port=6379,
        redis_password="",
        redis_db=0
    )
    print(f"  结果: {url2}")
    assert url2 == "redis://redis-server:6379/0"
    
    # 测试3: 优先使用完整 URL
    print("\n3. 测试优先使用完整 URL:")
    url3 = build_redis_url(
        redis_url="redis://:testpass@testhost:6380/3",
        redis_host="ignored",
        redis_port=9999,
        redis_password="ignored",
        redis_db=9
    )
    print(f"  结果: {url3}")
    assert url3 == "redis://:testpass@testhost:6380/3"
    
    # 测试4: 自定义端口和数据库
    print("\n4. 测试自定义端口和数据库:")
    url4 = build_redis_url(
        redis_host="custom-redis",
        redis_port=6380,
        redis_password="secret123",
        redis_db=10
    )
    print(f"  结果: {url4}")
    assert url4 == "redis://:secret123@custom-redis:6380/10"
    
    print("\n✅ 所有 Redis URL 构建测试通过！")

if __name__ == "__main__":
    test_redis_url_building()
