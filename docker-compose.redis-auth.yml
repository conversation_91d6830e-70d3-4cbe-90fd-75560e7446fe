version: '3.9'
services:
  api:
    build: .
    env_file: .env
    depends_on: [redis, postgres]
    ports:
      - "8000:8000"
  redis:
    image: redis:7
    restart: unless-stopped
    expose: ["6379"]
    command: redis-server --requirepass ${REDIS_PASSWORD:-myredispassword}
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-myredispassword}
  postgres:
    image: postgres:16
    restart: unless-stopped
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: mydb
    ports:
      - "5432:5432"
