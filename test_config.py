#!/usr/bin/env python3
"""测试配置读取"""
import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_config():
    """测试配置读取"""
    print("🔍 测试配置读取...")
    
    try:
        from img_describer.config import settings
        
        print("Redis配置:")
        print(f"  Host: {settings.redis_host}")
        print(f"  Port: {settings.redis_port}")
        print(f"  Password: {settings.redis_password}")
        print(f"  DB: {settings.redis_db}")
        print(f"  URL: {settings.redis_url}")
        print(f"  构建的URL: {settings.get_redis_url()}")
        
        print("\nPostgreSQL配置:")
        print(f"  User: {settings.pg_user}")
        print(f"  Password: {settings.pg_password}")
        print(f"  DB: {settings.pg_db}")
        print(f"  Host: {settings.pg_host}")
        print(f"  Port: {settings.pg_port}")
        
        print("\nOpenAI配置:")
        print(f"  Credentials: {settings.openai_credentials}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置读取失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_config()
