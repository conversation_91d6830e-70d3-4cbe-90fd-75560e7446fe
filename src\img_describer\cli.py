import asyncio
import argparse
from .service import init_resources, describe_image


async def main():
    parser = argparse.ArgumentParser(description="命令行图片描述 (low mode)")
    parser.add_argument("url", help="图片 URL")
    args = parser.parse_args()

    await init_resources()
    desc = await describe_image(args.url)
    print("\n图片描述:\n" + desc)


if __name__ == "__main__":
    asyncio.run(main())
