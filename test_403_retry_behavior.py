#!/usr/bin/env python3
"""测试403错误的重试行为"""
import sys
import os
import asyncio
import time
from typing import List, Dict

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_403_retry_behavior():
    """测试403错误的重试行为"""
    print("🔍 测试403错误的重试行为...")
    print("="*80)
    
    try:
        from img_describer.service import init_resources, describe_image
        from img_describer.openai_client_improved import describe_by_openai_improved
        from img_describer.openai_client import describe_by_openai
        
        # 初始化服务
        await init_resources()
        
        # 使用真实的产品ID生成图片URL
        from test_creat_img_url import parse_product_url, nm_ids
        
        # 选择一个真实的产品ID
        test_product_id = nm_ids[0]  # "449105617"
        test_url = parse_product_url(test_product_id, 1)['parse_product_urls'][0]
        
        print(f"📋 测试产品ID: {test_product_id}")
        print(f"📋 测试URL: {test_url}")
        print()
        
        # 测试1: 使用改进版客户端
        print("🧪 测试1: 使用改进版OpenAI客户端 (openai_client_improved)")
        print("-" * 60)
        
        start_time = time.time()
        try:
            result = await describe_by_openai_improved(test_url)
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ 改进版客户端成功")
            print(f"⏱️  耗时: {duration:.2f}秒")
            print(f"📝 描述: {result[:100]}...")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"❌ 改进版客户端失败")
            print(f"⏱️  耗时: {duration:.2f}秒")
            print(f"🚫 错误类型: {type(e).__name__}")
            print(f"🚫 错误信息: {str(e)}")
            
            # 分析是否进行了重试
            if duration > 10:  # 如果耗时超过10秒，说明可能进行了重试
                print(f"🔄 检测到可能的重试行为 (耗时{duration:.0f}秒)")
                
                # 分析重试次数
                if "403" in str(e):
                    print(f"⚠️  403错误 - 检查是否会重试:")
                    if duration < 5:
                        print(f"   ❌ 没有重试 (立即失败)")
                    elif 5 <= duration < 15:
                        print(f"   🔄 可能重试了1-2次")
                    elif 15 <= duration < 30:
                        print(f"   🔄 可能重试了3-4次")
                    else:
                        print(f"   🔄 可能重试了5次或更多")
            else:
                print(f"⚡ 快速失败，没有重试")
        
        print()
        
        # 测试2: 使用原始客户端
        print("🧪 测试2: 使用原始OpenAI客户端 (openai_client)")
        print("-" * 60)
        
        start_time = time.time()
        try:
            result = await describe_by_openai(test_url)
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ 原始客户端成功")
            print(f"⏱️  耗时: {duration:.2f}秒")
            print(f"📝 描述: {result[:100]}...")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"❌ 原始客户端失败")
            print(f"⏱️  耗时: {duration:.2f}秒")
            print(f"🚫 错误类型: {type(e).__name__}")
            print(f"🚫 错误信息: {str(e)}")
            
            # 分析是否进行了重试
            if duration > 10:  # 如果耗时超过10秒，说明可能进行了重试
                print(f"🔄 检测到可能的重试行为 (耗时{duration:.0f}秒)")
                
                # 分析重试次数 (原始客户端固定10秒间隔，最多5次重试)
                if "403" in str(e):
                    print(f"⚠️  403错误 - 检查是否会重试:")
                    if duration < 5:
                        print(f"   ❌ 没有重试 (立即失败)")
                    elif 10 <= duration < 20:
                        print(f"   🔄 重试了1次 (10秒)")
                    elif 20 <= duration < 30:
                        print(f"   🔄 重试了2次 (20秒)")
                    elif 30 <= duration < 40:
                        print(f"   🔄 重试了3次 (30秒)")
                    elif 40 <= duration < 50:
                        print(f"   🔄 重试了4次 (40秒)")
                    elif duration >= 50:
                        print(f"   🔄 重试了5次 (50秒)")
            else:
                print(f"⚡ 快速失败，没有重试")
        
        print()
        
        # 测试3: 使用服务层接口
        print("🧪 测试3: 使用服务层接口 (service.describe_image)")
        print("-" * 60)
        
        start_time = time.time()
        try:
            result = await describe_image(test_url)
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ 服务层接口成功")
            print(f"⏱️  耗时: {duration:.2f}秒")
            print(f"📝 描述: {result[:100]}...")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"❌ 服务层接口失败")
            print(f"⏱️  耗时: {duration:.2f}秒")
            print(f"🚫 错误类型: {type(e).__name__}")
            print(f"🚫 错误信息: {str(e)}")
            
            # 分析是否进行了重试
            if duration > 10:
                print(f"🔄 检测到可能的重试行为 (耗时{duration:.0f}秒)")
            else:
                print(f"⚡ 快速失败，没有重试")
        
        print()
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        import traceback
        traceback.print_exc()

def analyze_retry_mechanism():
    """分析当前的重试机制"""
    print("🔍 分析当前的重试机制...")
    print("="*80)
    
    print("📋 重试机制分析:")
    print()
    
    print("1. 🎯 改进版客户端 (openai_client_improved.py):")
    print("   - 重试装饰器: @retry(stop=stop_after_attempt(5))")
    print("   - 重试条件: retry_if_exception_type(RateLimitedError)")
    print("   - 等待策略: wait_exponential(multiplier=2, min=2, max=60)")
    print("   - 错误处理:")
    print("     * openai.RateLimitError -> RateLimitedError (会重试)")
    print("     * 其他Exception -> 直接抛出 (不会重试)")
    print("   - 403错误处理: 归类为其他Exception，不会重试")
    print()
    
    print("2. 🎯 原始客户端 (openai_client.py):")
    print("   - 重试装饰器: @retry(stop=stop_after_attempt(5))")
    print("   - 重试条件: retry_if_exception_type(RateLimitedError)")
    print("   - 等待策略: wait_fixed(10)")
    print("   - 错误处理:")
    print("     * openai.RateLimitError -> RateLimitedError (会重试)")
    print("     * 其他Exception -> 直接抛出 (不会重试)")
    print("   - 403错误处理: 归类为其他Exception，不会重试")
    print()
    
    print("3. ⚠️  关键发现:")
    print("   - 403错误 (User disabled) 不是 RateLimitError")
    print("   - 403错误会被归类为普通Exception")
    print("   - 普通Exception不在重试范围内")
    print("   - 因此403错误不会触发重试机制")
    print()
    
    print("4. 🚨 问题总结:")
    print("   - 当API密钥被禁用时，会返回403错误")
    print("   - 403错误不会触发重试，直接失败")
    print("   - 即使有多个API密钥，也不会自动切换")
    print("   - 需要手动处理403错误并切换到其他密钥")

async def main():
    """主函数"""
    print("🚀 开始403错误重试行为测试...")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 分析重试机制
    analyze_retry_mechanism()
    print()
    
    # 测试403重试行为
    await test_403_retry_behavior()
    
    print("🎉 403错误重试行为测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
