from typing import Optional
import redis.asyncio as redis
from .config import settings
from .logging import logger, GREEN


class RedisCache:
    def __init__(self):
        self._client: Optional[redis.Redis] = None

    async def init(self):
        redis_url = settings.get_redis_url()
        self._client = redis.from_url(redis_url, encoding="utf-8", decode_responses=True)
        # 测试连接
        await self._client.ping()
        logger.info(GREEN + f"Redis 连接成功: {redis_url}")

    async def get(self, key: str) -> Optional[str]:
        return await self._client.get(key)

    async def set(self, key: str, value: str):
        await self._client.set(key, value)
