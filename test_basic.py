#!/usr/bin/env python3
"""基础功能测试"""
import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_imports():
    """测试基础导入"""
    print("=== 测试基础导入 ===")
    
    try:
        # 测试配置模块
        from img_describer.config import settings
        print("✓ config 模块导入成功")
        print(f"  Redis Host: {settings.redis_host}")
        print(f"  Redis Port: {settings.redis_port}")
        print(f"  Redis DB: {settings.redis_db}")
        
        # 测试 Redis URL 构建
        redis_url = settings.get_redis_url()
        print(f"  Redis URL: {redis_url}")
        
        # 测试日志模块
        from img_describer.logging import logger, GREEN, RED
        print("✓ logging 模块导入成功")
        
        print("\n🎉 基础模块导入测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_redis_url_building():
    """测试 Redis URL 构建"""
    print("\n=== 测试 Redis URL 构建 ===")
    
    try:
        from img_describer.config import settings
        
        # 测试默认配置
        url1 = settings.get_redis_url()
        print(f"默认配置 URL: {url1}")
        
        # 模拟有密码的配置
        settings.redis_password = "testpass"
        url2 = settings.get_redis_url()
        print(f"有密码配置 URL: {url2}")
        
        # 模拟使用完整 URL
        settings.redis_url = "redis://:mypass@external:6380/3"
        url3 = settings.get_redis_url()
        print(f"完整 URL 配置: {url3}")
        
        print("✓ Redis URL 构建测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ Redis URL 构建错误: {e}")
        return False

if __name__ == "__main__":
    success1 = test_basic_imports()
    success2 = test_redis_url_building()
    
    if success1 and success2:
        print("\n🎉 所有基础测试通过！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败")
        sys.exit(1)
