#!/usr/bin/env python3
"""测试服务连接"""
import sys
import os
import asyncio

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_redis_connection():
    """测试Redis连接"""
    print("🔍 测试Redis连接...")
    
    try:
        from img_describer.config import settings
        from img_describer.cache import RedisCache
        
        print(f"Redis配置: {settings.get_redis_url()}")
        
        cache = RedisCache()
        await cache.init()
        
        # 测试基本操作
        await cache.set("test_key", "test_value")
        value = await cache.get("test_key")
        
        if value == "test_value":
            print("✅ Redis连接成功！")
            return True
        else:
            print("❌ Redis读写测试失败")
            return False
            
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False

async def test_db_connection():
    """测试数据库连接"""
    print("\n🔍 测试PostgreSQL连接...")
    
    try:
        from img_describer.db import Database
        
        db = Database()
        print(f"数据库DSN: {db.dsn}")
        
        await db.init()
        print("✅ PostgreSQL连接成功！")
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL连接失败: {e}")
        return False

async def test_openai_config():
    """测试OpenAI配置"""
    print("\n🔍 测试OpenAI配置...")
    
    try:
        from img_describer.config import settings
        import json
        
        credentials = json.loads(settings.openai_credentials)
        print(f"OpenAI配置数量: {len(credentials)}")
        
        for i, cred in enumerate(credentials):
            print(f"  配置 {i+1}:")
            print(f"    Base URL: {cred.get('base_url', 'N/A')}")
            print(f"    Model: {cred.get('model', 'N/A')}")
            print(f"    API Key: {cred.get('api_key', 'N/A')[:20]}...")
        
        print("✅ OpenAI配置读取成功！")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI配置读取失败: {e}")
        return False

async def test_simple_describe():
    """测试简单的图片描述功能"""
    print("\n🔍 测试图片描述功能...")
    
    try:
        from img_describer.service import init_resources, describe_image
        
        print("初始化服务资源...")
        await init_resources()
        
        # 使用一个简单的测试图片URL
        test_url = "https://basket-25.wbbasket.ru/vol4491/part449105/449105617/images/big/1.webp"
        print(f"测试URL: {test_url}")
        
        print("正在调用描述服务...")
        description = await describe_image(test_url)
        
        print(f"✅ 描述成功!")
        print(f"📝 描述内容: {description}")
        return True
        
    except Exception as e:
        print(f"❌ 图片描述失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始连接测试...")
    
    # 测试各个组件
    redis_ok = await test_redis_connection()
    db_ok = await test_db_connection()
    openai_ok = await test_openai_config()
    
    print(f"\n{'='*50}")
    print("📊 连接测试结果:")
    print(f"Redis: {'✅' if redis_ok else '❌'}")
    print(f"PostgreSQL: {'✅' if db_ok else '❌'}")
    print(f"OpenAI配置: {'✅' if openai_ok else '❌'}")
    
    if redis_ok and db_ok and openai_ok:
        print("\n🎉 所有服务连接正常，开始功能测试...")
        describe_ok = await test_simple_describe()
        
        if describe_ok:
            print("\n🎉 所有测试通过！服务运行正常。")
            return True
        else:
            print("\n⚠️  服务连接正常，但功能测试失败。")
            return False
    else:
        print("\n❌ 部分服务连接失败，请检查配置。")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
