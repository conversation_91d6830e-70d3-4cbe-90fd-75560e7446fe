#!/usr/bin/env python3
"""测试真实配置"""
import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_real_config():
    """测试真实配置"""
    print("🔍 测试真实配置...")
    
    # 重新导入配置
    import importlib
    if 'img_describer.config' in sys.modules:
        importlib.reload(sys.modules['img_describer.config'])
    
    from img_describer.config import settings
    
    print("Redis配置:")
    print(f"  Host: {settings.redis_host}")
    print(f"  Port: {settings.redis_port}")
    print(f"  Password: {settings.redis_password}")
    print(f"  DB: {settings.redis_db}")
    print(f"  构建的URL: {settings.get_redis_url()}")
    
    print("\nPostgreSQL配置:")
    print(f"  User: {settings.pg_user}")
    print(f"  Password: {settings.pg_password}")
    print(f"  DB: {settings.pg_db}")
    print(f"  Host: {settings.pg_host}")
    print(f"  Port: {settings.pg_port}")
    
    print("\nOpenAI配置:")
    print(f"  Credentials: {settings.openai_credentials[:100]}...")
    
    return settings

if __name__ == "__main__":
    settings = test_real_config()
    
    # 验证配置是否正确
    expected_redis_host = "************"
    expected_pg_host = "************"
    
    if settings.redis_host == expected_redis_host and settings.pg_host == expected_pg_host:
        print("\n✅ 配置验证通过！")
    else:
        print(f"\n❌ 配置验证失败！")
        print(f"期望 Redis Host: {expected_redis_host}, 实际: {settings.redis_host}")
        print(f"期望 PG Host: {expected_pg_host}, 实际: {settings.pg_host}")
