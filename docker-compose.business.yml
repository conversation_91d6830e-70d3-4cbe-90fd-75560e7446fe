# Docker Compose 配置 - 业务服务独立部署
# 用于部署业务服务，连接到外部的基础设施服务
# 适用于不同项目的业务服务独立部署

version: '3.9'

networks:
  microservices:
    # 连接到外部的基础设施网络
    name: microservices
    external: true

services:
  # 图片描述服务
  say-img-description:
    build: .
    container_name: say-img-description-server
    hostname: say-img-description
    environment:
      # Consul 配置 - 连接到外部 Consul
      - CONSUL_HOST=${CONSUL_HOST:-consul}
      - CONSUL_PORT=${CONSUL_PORT:-8500}
      
      # 服务配置
      - SERVICE_NAME=say-img-description
      - SERVICE_PORT=${SERVICE_PORT:-8000}
      - SERVICE_TAGS=${SERVICE_TAGS:-api,image,ai}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      
      # 数据库配置 - 连接到外部数据库
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}

      # 兼容原有配置格式
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DB=${REDIS_DB}
      - PG_USER=${PG_USER}
      - PG_PASSWORD=${PG_PASSWORD}
      - PG_DB=${PG_DB}
      - PG_HOST=${PG_HOST}
      - PG_PORT=${PG_PORT}
      
      # 应用配置
      - HOST=0.0.0.0
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      
      # OpenAI 配置
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_CREDENTIALS=${OPENAI_CREDENTIALS}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4-vision-preview}
    env_file:
      - .env
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:${SERVICE_PORT:-8000}/health"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    # 暴露端口用于测试
    ports:
      - "${SERVICE_PORT:-8000}:${SERVICE_PORT:-8000}"
    command: ["python", "run_consul_api.py"]

  # 可选：如果业务服务需要自己的数据库实例
  # business-postgres:
  #   image: postgres:16-alpine
  #   container_name: ${SERVICE_NAME:-business}-postgres
  #   hostname: ${SERVICE_NAME:-business}-postgres
  #   environment:
  #     POSTGRES_USER: ${DB_USER:-business_user}
  #     POSTGRES_PASSWORD: ${DB_PASSWORD:-business_password}
  #     POSTGRES_DB: ${DB_NAME:-business_db}
  #     POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
  #   volumes:
  #     - business_postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - microservices
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-business_user} -d ${DB_NAME:-business_db}"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5
  #     start_period: 10s
  #   restart: unless-stopped

  # 可选：如果业务服务需要自己的 Redis 实例
  # business-redis:
  #   image: redis:7-alpine
  #   container_name: ${SERVICE_NAME:-business}-redis
  #   hostname: ${SERVICE_NAME:-business}-redis
  #   volumes:
  #     - business_redis_data:/data
  #   networks:
  #     - microservices
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 3
  #     start_period: 5s
  #   restart: unless-stopped

# 可选：业务服务专用数据卷
# volumes:
#   business_postgres_data:
#     name: ${SERVICE_NAME:-business}_postgres_data
#   business_redis_data:
#     name: ${SERVICE_NAME:-business}_redis_data
