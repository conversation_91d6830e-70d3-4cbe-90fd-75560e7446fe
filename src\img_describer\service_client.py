"""
微服务间调用客户端
基于 Consul 服务发现的 HTTP 客户端
"""
import asyncio
import aiohttp
import json
from typing import Optional, Dict, Any, Union
from .consul_service import consul_service
from .logging import logger, GREEN, RED


class ServiceClient:
    """微服务调用客户端"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.timeout = aiohttp.ClientTimeout(total=30)
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取或创建 HTTP 会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self.timeout)
        return self.session
    
    async def close(self):
        """关闭客户端会话"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def call_service(
        self,
        service_name: str,
        endpoint: str,
        method: str = "GET",
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        retry_count: int = 3
    ) -> Optional[Dict[str, Any]]:
        """
        调用其他微服务
        
        Args:
            service_name: 目标服务名称
            endpoint: API 端点路径
            method: HTTP 方法
            data: 请求体数据
            params: 查询参数
            headers: 请求头
            retry_count: 重试次数
            
        Returns:
            响应数据或 None
        """
        for attempt in range(retry_count):
            try:
                # 发现服务实例
                service_instance = await consul_service.discover_service(service_name)
                if not service_instance:
                    logger.error(RED + f"✗ 未找到服务实例: {service_name}")
                    return None
                
                # 构建请求 URL
                base_url = f"http://{service_instance['address']}:{service_instance['port']}"
                url = f"{base_url}{endpoint}"
                
                # 准备请求参数
                request_kwargs = {
                    "url": url,
                    "params": params,
                    "headers": headers or {}
                }
                
                # 添加请求体
                if data is not None:
                    if method.upper() in ["POST", "PUT", "PATCH"]:
                        request_kwargs["json"] = data
                        request_kwargs["headers"]["Content-Type"] = "application/json"
                
                # 发送请求
                session = await self._get_session()
                async with session.request(method.upper(), **request_kwargs) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(GREEN + f"✓ 服务调用成功: {service_name}{endpoint}")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(RED + f"✗ 服务调用失败: {service_name}{endpoint} - {response.status}: {error_text}")
                        
                        # 如果是客户端错误（4xx），不重试
                        if 400 <= response.status < 500:
                            return None
                            
            except Exception as e:
                logger.error(RED + f"✗ 服务调用异常: {service_name}{endpoint} - {e}")
                
                # 如果不是最后一次尝试，等待后重试
                if attempt < retry_count - 1:
                    await asyncio.sleep(1 * (attempt + 1))  # 指数退避
                    continue
        
        logger.error(RED + f"✗ 服务调用最终失败: {service_name}{endpoint}")
        return None
    
    async def get(
        self,
        service_name: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Optional[Dict[str, Any]]:
        """GET 请求"""
        return await self.call_service(
            service_name=service_name,
            endpoint=endpoint,
            method="GET",
            params=params,
            headers=headers
        )
    
    async def post(
        self,
        service_name: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Optional[Dict[str, Any]]:
        """POST 请求"""
        return await self.call_service(
            service_name=service_name,
            endpoint=endpoint,
            method="POST",
            data=data,
            params=params,
            headers=headers
        )
    
    async def put(
        self,
        service_name: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Optional[Dict[str, Any]]:
        """PUT 请求"""
        return await self.call_service(
            service_name=service_name,
            endpoint=endpoint,
            method="PUT",
            data=data,
            params=params,
            headers=headers
        )
    
    async def delete(
        self,
        service_name: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Optional[Dict[str, Any]]:
        """DELETE 请求"""
        return await self.call_service(
            service_name=service_name,
            endpoint=endpoint,
            method="DELETE",
            params=params,
            headers=headers
        )
    
    async def health_check_service(self, service_name: str) -> bool:
        """检查目标服务健康状态"""
        try:
            result = await self.get(service_name, "/health")
            return result is not None and result.get("status") == "healthy"
        except Exception as e:
            logger.error(RED + f"✗ 健康检查失败: {service_name} - {e}")
            return False
    
    async def get_service_info(self, service_name: str) -> Optional[Dict[str, Any]]:
        """获取目标服务信息"""
        try:
            return await self.get(service_name, "/info")
        except Exception as e:
            logger.error(RED + f"✗ 获取服务信息失败: {service_name} - {e}")
            return None
    
    # 特定服务的便捷方法（可以根据实际需要添加）
    async def describe_image(self, image_url: str) -> Optional[str]:
        """调用图片描述服务"""
        try:
            result = await self.get(
                service_name="say-img-description",
                endpoint="/describe",
                params={"url": image_url}
            )
            return result.get("description") if result else None
        except Exception as e:
            logger.error(RED + f"✗ 图片描述服务调用失败: {e}")
            return None


# 全局实例
service_client = ServiceClient()


# 清理函数，在应用关闭时调用
async def cleanup_service_client():
    """清理服务客户端资源"""
    await service_client.close()
    logger.info("ServiceClient 资源已清理")
