# Consul-Template 动态 Nginx 配置模板
# 此文件由 consul-template 根据 Consul 服务发现自动生成

{{- range services }}
  {{- if .Tags | contains "api" }}

# 上游服务器配置: {{ .Name }}
upstream {{ .Name | replaceAll "-" "_" }}_backend {
    least_conn;
    {{- range service .Name }}
    server {{ .Address }}:{{ .Port }} max_fails=3 fail_timeout=30s weight=1;
    {{- end }}
    
    # 健康检查（如果支持）
    # health_check uri=/health interval=10s fails=3 passes=2;
}

  {{- end }}
{{- end }}

# 动态路由配置
server {
    listen 80;
    server_name _;

    # 全局设置
    client_max_body_size 100M;
    proxy_connect_timeout 5s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

{{- range services }}
  {{- if .Tags | contains "api" }}
    
    # 服务路由: {{ .Name }}
    location /api/{{ .Name }}/ {
        # 移除路径前缀
        rewrite ^/api/{{ .Name }}/(.*)$ /$1 break;
        
        # 代理到上游服务器
        proxy_pass http://{{ .Name | replaceAll "-" "_" }}_backend;
        
        # 代理头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Service-Name {{ .Name }};
        
        # 错误处理
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 10s;
        
        # 添加服务标识头
        add_header X-Served-By {{ .Name }};
        add_header X-Service-Version "1.0.0";
    }
    
    # 健康检查路由: {{ .Name }}
    location /api/{{ .Name }}/health {
        proxy_pass http://{{ .Name | replaceAll "-" "_" }}_backend/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 健康检查不缓存
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # 服务信息路由: {{ .Name }}
    location /api/{{ .Name }}/info {
        proxy_pass http://{{ .Name | replaceAll "-" "_" }}_backend/info;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

  {{- end }}
{{- end }}

    # 服务发现 API
    location /api/services {
        return 200 '{
            "services": [
{{- $first := true }}
{{- range services }}
  {{- if .Tags | contains "api" }}
    {{- if not $first }},{{ end }}
                {
                    "name": "{{ .Name }}",
                    "tags": {{ .Tags | toJSON }},
                    "instances": [
{{- $instanceFirst := true }}
{{- range service .Name }}
        {{- if not $instanceFirst }},{{ end }}
                        {
                            "id": "{{ .ID }}",
                            "address": "{{ .Address }}",
                            "port": {{ .Port }},
                            "health": "{{ .Status }}"
                        }
        {{- $instanceFirst = false }}
{{- end }}
                    ]
                }
    {{- $first = false }}
  {{- end }}
{{- end }}
            ]
        }';
        add_header Content-Type application/json;
    }

    # 网关状态
    location /gateway/status {
        return 200 '{
            "gateway": "nginx",
            "status": "running",
            "version": "1.0.0",
            "timestamp": "{{ timestamp }}",
            "services_count": {{ services | len }}
        }';
        add_header Content-Type application/json;
    }

    # 默认路由
    location / {
        return 200 '{
            "message": "API Gateway",
            "status": "running",
            "endpoints": {
                "/api/services": "服务发现",
                "/gateway/status": "网关状态",
                "/consul/": "Consul UI"
            }
        }';
        add_header Content-Type application/json;
    }

    # 错误处理
    error_page 404 = @not_found;
    location @not_found {
        return 404 '{
            "error": "Not Found",
            "message": "The requested service or endpoint was not found",
            "available_services": [
{{- $first := true }}
{{- range services }}
  {{- if .Tags | contains "api" }}
    {{- if not $first }},{{ end }}
                "/api/{{ .Name }}/"
    {{- $first = false }}
  {{- end }}
{{- end }}
            ]
        }';
        add_header Content-Type application/json;
    }

    error_page 500 502 503 504 = @server_error;
    location @server_error {
        return 500 '{
            "error": "Server Error",
            "message": "The server encountered an error while processing the request"
        }';
        add_header Content-Type application/json;
    }
}
