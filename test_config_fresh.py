#!/usr/bin/env python3
"""重新测试配置读取"""
import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 清除可能的模块缓存
if 'img_describer.config' in sys.modules:
    del sys.modules['img_describer.config']

def test_config_fresh():
    """重新测试配置读取"""
    print("🔍 重新测试配置读取...")
    
    try:
        # 直接创建新的配置实例
        from pydantic_settings import BaseSettings
        
        class FreshSettings(BaseSettings):
            redis_host: str = "default_redis"
            redis_port: int = 6379
            redis_password: str = ""
            redis_db: int = 5
            
            pg_user: str = "default_user"
            pg_password: str = "default_password"
            pg_db: str = "default_db"
            pg_host: str = "default_host"
            pg_port: int = 5432
            
            openai_credentials: str = "[]"
            
            model_config = {
                "env_file": ".env",
                "env_file_encoding": "utf-8"
            }
            
            def get_redis_url(self) -> str:
                """获取 Redis 连接 URL"""
                if hasattr(self, 'redis_url') and self.redis_url:
                    return self.redis_url
                
                # 构建 Redis URL
                auth_part = f":{self.redis_password}@" if self.redis_password else ""
                return f"redis://{auth_part}{self.redis_host}:{self.redis_port}/{self.redis_db}"
        
        settings = FreshSettings()
        
        print("Redis配置:")
        print(f"  Host: {settings.redis_host}")
        print(f"  Port: {settings.redis_port}")
        print(f"  Password: {settings.redis_password}")
        print(f"  DB: {settings.redis_db}")
        print(f"  构建的URL: {settings.get_redis_url()}")
        
        print("\nPostgreSQL配置:")
        print(f"  User: {settings.pg_user}")
        print(f"  Password: {settings.pg_password}")
        print(f"  DB: {settings.pg_db}")
        print(f"  Host: {settings.pg_host}")
        print(f"  Port: {settings.pg_port}")
        
        print("\nOpenAI配置:")
        print(f"  Credentials: {settings.openai_credentials[:100]}...")
        
        return settings
        
    except Exception as e:
        print(f"❌ 配置读取失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    settings = test_config_fresh()
    if settings:
        print("\n✅ 配置读取成功！")
    else:
        print("\n❌ 配置读取失败！")
