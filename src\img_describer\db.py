from typing import Optional
import asyncpg
from .config import settings
from .logging import logger, GREEN

TABLE = "pj_similar.say_img_description"


class Database:
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None

    @property
    def dsn(self):
        return f"postgresql://{settings.pg_user}:{settings.pg_password}@{settings.pg_host}:{settings.pg_port}/{settings.pg_db}"

    async def init(self):
        self.pool = await asyncpg.create_pool(self.dsn)
        async with self.pool.acquire() as conn:
            # 创建 schema（如果不存在）
            await conn.execute("CREATE SCHEMA IF NOT EXISTS pj_similar;")

            # 创建表
            await conn.execute(
                f"""
                CREATE TABLE IF NOT EXISTS {TABLE} (
                    id SERIAL PRIMARY KEY,
                    photo_url TEXT UNIQUE NOT NULL,
                    img_description TEXT NOT NULL,
                    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
                    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
                );
                """
            )
        logger.info(GREEN + "PostgreSQL 连接成功，schema 和表已确保存在")

    async def fetch_desc(self, url: str) -> Optional[str]:
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow(f"SELECT img_description FROM {TABLE} WHERE photo_url=$1", url)
            return row[0] if row else None

    async def upsert_desc(self, url: str, desc: str):
        async with self.pool.acquire() as conn:
            await conn.execute(
                f"""
                INSERT INTO {TABLE} (photo_url, img_description)
                VALUES ($1, $2)
                ON CONFLICT (photo_url)
                DO UPDATE SET img_description = EXCLUDED.img_description,
                              updated_at = now();
                """,
                url,
                desc,
            )
